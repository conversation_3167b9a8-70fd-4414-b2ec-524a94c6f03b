<!-- https://futurestud.io/tutorials/vue-js-3-auto-resize-textarea -->
<!-- https://github.com/zernonia/vue0/blob/main/components/PromptInput.vue -->
 
<script setup lang="ts">
import { useTextareaAutosize, useMagicKeys, useVModel } from '@vueuse/core'
import { onMounted } from 'vue'

const props = withDefaults(
  defineProps<{
    modelValue?: string
    loading?: boolean
    disabled?: boolean
    placeholder?: string
    disableNewlines?: boolean // 新增：是否禁止换行
  }>(),
  {
    placeholder: 'Type something...'
  }
)

const emits = defineEmits<{
  'update:modelValue': [payload: string]
}>()

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: ''
})

const { textarea: textareaTarget, input: textareaInput, } = useTextareaAutosize()
const textareaFocused = ref(false)

// 修改 watch 监听确保双向同步
watch(textareaInput, (newInput) => {
  modelValue.value = newInput
})

watch(modelValue, (newModelValue) => {
  if (textareaInput.value !== newModelValue) {
    textareaInput.value = newModelValue || ''
  }
})

// 拦截 Enter 键
const handleKeydown = (event: KeyboardEvent) => {
  if (props.disableNewlines && event.key === 'Enter') {
    event.preventDefault() // 阻止默认换行行为
  }
}

// 过滤粘贴内容中的换行符
const handlePaste = (event: ClipboardEvent) => {
  if (props.disableNewlines) {
    event.preventDefault() // 阻止默认粘贴行为
    const text = event.clipboardData?.getData('text') || ''
    const filteredText = text.replace(/\n/g, '') // 过滤换行符去除换行
    document.execCommand('insertText', false, filteredText) // 插入过滤后的文本
  }
}

useMagicKeys({
  passive: false,
  onEventFired(e) {
    if (e.code === 'Slash' && e.type === 'keydown' && document.activeElement !== textareaTarget.value) {
      e.preventDefault()
      textareaTarget.value?.focus()
    }
  },
})

onMounted(() => {
  textareaInput.value = modelValue.value || ''
})
</script>

<template>
  <div class="flex items-end px-2 md:px-4 py-2 text-sm border rounded focus-within:ring-1 focus-within:ring-primary">
    <textarea
      ref="textareaTarget"
      v-model="textareaInput"
      :disabled="loading || disabled"
      spellcheck="false"
      @keydown="handleKeydown"
      @paste="handlePaste"
      class="outline-hidden resize-none my-0 min-h-[20px] no-scrollbar w-full bg-transparent px-1"
      :placeholder="`${placeholder}`"
      @focus="textareaFocused = true"
      @blur="textareaFocused = false"
    />
  </div>
</template>