<script setup lang="ts">
import { shallowRef, watchEffect, ref, computed, watch, onMounted, onBeforeUnmount, nextTick, type ComponentPublicInstance } from 'vue'
import { TreeRoot, TreeItem, type FlattenedItem } from 'reka-ui'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import Icon from '@/components/Icon.vue'
import { Button } from '@/components/ui/button'
import { flattenBookmarks, findParentPath } from '@/utils/bookmark'

const bookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const folders = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const expandedIds = ref<string[]>([])

const props = withDefaults(defineProps<{
  currentFolderId: string
}>(), {})

const emit = defineEmits<{
  folderSelected: [folderId: string]
}>()

// 自动滚动到当前文件夹
const scrollAreaRef = ref<InstanceType<typeof ScrollArea>>();
const currentItemRef = ref<HTMLElement>();
const scrollToCurrentFolder = () => {
  nextTick(() => {
    if (!currentItemRef.value || !scrollAreaRef.value) return;
    
    const viewport = scrollAreaRef.value.$el.querySelector('[data-reka-scroll-area-viewport]');
    const targetElement = currentItemRef.value as unknown as HTMLElement;
    console.log('scrollToCurrentFolder', viewport, targetElement);

    if (viewport && targetElement) {
      console.log('scrollToCurrentFolder', viewport, targetElement);

      const targetTop = targetElement.offsetTop - viewport.offsetHeight / 3;
      viewport.scrollTo({
        top: targetTop,
        behavior: 'smooth'
      });
    }
  });
};

// 搜索
const searchQuery = ref(''); // 原始搜索关键词
const searchedFolders = ref<chrome.bookmarks.BookmarkTreeNode[]>([]) // 搜索结果
const searchedFolderIndex = ref(0);
const search = async () => {
  const flattenedFolders = flattenBookmarks(folders.value);
  searchedFolders.value = flattenedFolders.filter(folder => folder.title.toLowerCase().includes(searchQuery.value.toLowerCase()));
  searchedFolderIndex.value = 0;
}
// 将当前文件夹设置为搜索结果的第一个结果，每点击一次，改为下一个搜索结果
const jumpToSearchFolder = () => {
  if (searchedFolders.value.length === 0) return;
  const searchedFolder = searchedFolders.value[searchedFolderIndex.value];
  emit('folderSelected', searchedFolder.id);
  if (searchedFolderIndex.value < searchedFolders.value.length - 1) {
    searchedFolderIndex.value++;
  } else {
    searchedFolderIndex.value = 0;
  }
  scrollToCurrentFolder();
}
// 判断文件夹是否匹配搜索关键词
const isFolderMatched = (folder: any) => {
  if (!searchQuery.value) return true; // 如果防抖后的搜索关键词为空，所有文件夹都匹配
  const title = folder.raw?.title || folder.bind?.value?.title; // 从 folder 或 bind.value 中获取 title
  return title && title.toLowerCase().includes(searchQuery.value.toLowerCase());
};
watch(searchQuery, () => {
  search();
})

// 确保当前文件夹及其所有父级都展开
function ensureFolderExpanded(folderId: string) {
  const parentPath = findParentPath(folders.value, folderId);
  if (parentPath) {
    expandedIds.value = Array.from(new Set([...expandedIds.value, ...parentPath]));
  }
}

// 修改 watch currentFolderId 的监听器
watch(() => props.currentFolderId, async (newVal) => {
  ensureFolderExpanded(newVal);
  await nextTick();
  // scrollToCurrentFolder();
});

// 递归函数，过滤掉所有没有 children 的项目
function getFolders(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  return bookmarks.filter(bookmark => {
    if (bookmark.children) {
      bookmark.children = getFolders(bookmark.children); // 递归过滤子项
      return true;
    }
    return false;
  });
}

// 递归提取所有节点的 id
function extractIds(nodes: chrome.bookmarks.BookmarkTreeNode[]) {
  return nodes.map(node => {
    // 将当前节点的id添加到结果数组中
    const ids = [node.id];
    // 如果当前节点有子节点，递归提取子节点的id
    if (node.children) {
      ids.push(...extractIds(node.children));
    }
    return ids;
  }).flat();
}

function handleExpandedUpdate(ids: string[]) {
  expandedIds.value = ids;
}

watchEffect((onCleanup) => {
  const dndFunction = combine(
    monitorForElements({
      onDrop(args) {
        const { location, source } = args;

        // didn't drop on anything
        if (!location.current.dropTargets.length)
          return;

        const itemId = source.data.id as string;
        const target = location.current.dropTargets[0];
        const targetId = target.data.id as string;

        const instruction: Instruction | null = extractInstruction(
          target.data,
        );

        if (instruction !== null && instruction.type === 'make-child') {
          browser.bookmarks.move(itemId, { parentId: targetId });
        }
        if (instruction !== null && instruction.type === 'reorder-above') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex });
        }
        return;
        // if (instruction !== null) {
        //   items.value = updateTree(items.value, {
        //     type: 'instruction',
        //     instruction,
        //     itemId,
        //     targetId,
        //   }) ?? [];
        // }
      },
    }),
  );

  onCleanup(() => {
    dndFunction();
  });
});

async function setBookmarks() {
  const start = performance.now();
  bookmarks.value = await browser.bookmarks.getTree();
  folders.value = getFolders(bookmarks.value);
  expandedIds.value = extractIds(folders.value);
  const end = performance.now();
  console.log(`FolderTree getAllBookmark took ${(end - start).toFixed(0)} milliseconds`, bookmarks.value, folders.value);
}

function handleRuntimeMessage(request: any) {
  console.warn('--------- sidepanel FolderTree 收到消息 -----------!', request);
  if (request.type === 'bookmark-change') {
    setBookmarks();
  }
}

onMounted(async () => {
  setBookmarks();
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
});

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
});

// FolderTreeItem 的点击处理函数
function handleFolderItemClick(event: Event, folderId: string) {
  emit('folderSelected', folderId);
  event.stopImmediatePropagation();
}
</script>

<template>
  <div class="flex flex-col justify-center w-full relative">
      <!-- 搜索框 -->
      <div class="relative w-full items-center z-10">
        <Input
          v-model="searchQuery"
          :placeholder="i18n.t('popup.searchFolders')"
          class="rounded-none rounded-t-md pl-8 text-sm focus-visible:ring-0"
          @keydown.enter.prevent="jumpToSearchFolder()"
        />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
          <Icon name="lucide:search" class="size-4 text-muted-foreground" />
        </span>
        <Button 
          class="absolute end-0 inset-y-0 m-1 h-7"
          :class="{ 'hidden': !searchQuery }"
          type="button"
          :disabled="searchedFolders.length === 0"
          @click.prevent="jumpToSearchFolder()"
        >
          <Icon name="lucide:corner-down-left" class="size-3" /> 
          <span class="text-xs">{{ searchedFolders.length }}</span>
        </Button>
      </div>

    <ScrollArea 
      ref="scrollAreaRef"
      type="auto" 
      class="h-[300px] overflow-visible border border-t-0 rounded-none rounded-b-md shadow-[inset_0_0_6px_rgba(0,0,0,0.1)]"
    >
      <!-- 本地文件夹树 -->
      <TreeRoot
        v-slot="{ flattenItems }"
        class="w-full list-none select-none rounded-md text-sm font-normal"
        :items="folders[0]?.children"
        :get-key="(item) => item.id"
        multiple
        propagate-select
        :expanded="expandedIds"
        @update:expanded="handleExpandedUpdate"
      >
        <TreeItem
          v-for="item in flattenItems"
          :key="item._id + item.index"
          v-slot="{ isExpanded, handleToggle }"
          :ref="(el: any) => {
            if (item.value.id === currentFolderId)
              currentItemRef = (el as ComponentPublicInstance)?.$el || el as HTMLElement
          }"
          v-bind="item.bind"
          class="relative w-full hover:bg-muted z-0"
          :class="[
            'flex items-center px-0 h-6 outline-hidden data-selected:bg-red-100',
            {
              'opacity-40': searchQuery && !isFolderMatched(item),
              'bg-primary/20 dark:bg-primary/50 font-semibold': currentFolderId === item.value.id
            },
          ]"
          @click.stop.prevent="handleFolderItemClick($event, item.value.id)"
          @select.prevent
          @contextmenu.native="() => { emit('folderSelected', item.value.id) }"
        >
          <!-- 添加一个包装容器来处理双击事件 -->
          <div class="flex items-center w-full" @dblclick.stop="handleToggle">
            <div v-for="level in item.level" :key="level" class="w-2 min-w-2 h-full border-l ml-3 z-0" :class="{'hidden' : level === 1}"></div>
            <div
              v-if="item.value.children && item.value.children.length > 0"
              class="hover:bg-gray-300/50 py-1 px-1 rounded-full"
              @click.stop="handleToggle" >
              <Icon
                name="radix-icons:triangle-right"
                class="h-4 w-4 transition"
                :class="{ 'rotate-90': isExpanded }"
              />
            </div>
            <div v-else class="h-4 w-6"></div>
            <div class="flex pl-0 items-center" >
              <Icon name="lucide:folder" class="size-3.5 transition" />
              <span class="pl-2 truncate">{{ item.value.title }}</span>
            </div>
          </div>
        </TreeItem>
      </TreeRoot>
    </ScrollArea>
  </div>
</template>