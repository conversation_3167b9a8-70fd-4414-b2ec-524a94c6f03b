<script setup lang="ts">
import { ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import FolderBreadcrumbs from '@/components/FolderBreadcrumbs.vue'
import FolderSeletor from '@/components/FolderSeletor.vue'
import Icon from '@/components/Icon.vue'
import useProfileUser from '@/composables/useProfileUser'
import { hashString } from '@/utils'
import { rxDBInstance } from '@/rxdb/index'

const props = withDefaults(defineProps<{
  open: boolean
  defaultFolderId?: string
}>(), {
  defaultFolderId: ''
})

const emit = defineEmits<{
  openChange: [value: boolean]
  shareCreated: [data: { folderId: string; isPublic: boolean; shareUrl: string }]
}>()

const endpoint = import.meta.env.VITE_ENDPOINT
const loading = ref(false)
const selectedFolder = ref<chrome.bookmarks.BookmarkTreeNode | null>(null)
const selectedFolderId = ref('')
const isPublic = ref(true)

// 处理文件夹选择
const handleFolderSelect = (folderId: string) => {
  selectedFolderId.value = folderId
}

// 监听 selectedFolderId 变化，自动获取对应的 selectedFolder
watch(selectedFolderId, async (newFolderId) => {
  if (!newFolderId) {
    selectedFolder.value = null
    return
  }

  try {
    const results = await browser.bookmarks.get(newFolderId)
    if (results[0]) {
      selectedFolder.value = results[0]
    }
  } catch (error) {
    console.error('Error getting folder details:', error)
    selectedFolder.value = null
  }
}, { immediate: true })

// 创建分享
const onSubmit = async () => {
  // 简单验证
  if (!selectedFolderId.value) {
    toast.error(i18n.t('share.selectFolder'))
    return
  }

  try {
    loading.value = true

    // 获取当前用户信息
    const { currentProfileUser } = useProfileUser()

    // 获取文件夹的完整数据
    const folderSubTree = await browser.bookmarks.getSubTree(selectedFolderId.value)
    const bookmarkData = JSON.stringify(folderSubTree)

    // 生成数据哈希 (使用改进哈希算法，平衡性能和可靠性)
    const bookmarkDataHash = hashString(bookmarkData)

    // 获取当前浏览器类型
    const currentBrowser = import.meta.env.BROWSER || 'chrome'

    // 准备提交到服务器的数据
    const submissionData = {
      source: currentBrowser,
      browserAccountId: currentProfileUser.value?.id || '',
      bookmarkId: selectedFolderId.value,
      bookmarkData: bookmarkData,
      bookmarkDataHash: bookmarkDataHash,
      folderId: selectedFolderId.value,
      isPublic: isPublic.value
    }

    // Step 1: 发送数据到服务器创建共享文件夹记录
    const res = await fetch(`${endpoint}/api/dashboard/shareds`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(submissionData)
    })

    if (!res.ok) {
      throw new Error(`Server error: ${res.status} ${res.statusText}`)
    }

    const responseData = await res.json()

    // Step 2: 合并服务器响应和原始提交数据，创建本地 RxDB 记录
    const localRecordData = {
      ...submissionData,
      ...responseData,
    }

    // 插入到本地 RxDB
    await rxDBInstance.shareds.insert(localRecordData)

    // 发出分享创建成功事件
    emit('shareCreated', {
      folderId: selectedFolderId.value,
      isPublic: isPublic.value,
      shareUrl: responseData.shareUrl
    })

    // 复制链接到剪贴板
    await navigator.clipboard.writeText(responseData.shareUrl)

    toast.success(i18n.t('share.shareCreated'))
    toast.success(i18n.t('share.linkCopied'))

    // 关闭对话框
    emit('openChange', false)

  } catch (error) {
    console.error('Error creating share:', error)

    // 提供更具体的错误信息
    if (error instanceof Error) {
      if (error.message.includes('Server error')) {
        toast.error(i18n.t('error.serverError') || 'Server error occurred')
      } else if (error.message.includes('insert')) {
        toast.error(i18n.t('error.localStorageError') || 'Failed to save locally')
      } else {
        toast.error(error.message)
      }
    } else {
      toast.error(i18n.t('error.unknown'))
    }
  } finally {
    loading.value = false
  }
}



// 监听对话框打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    selectedFolderId.value = props.defaultFolderId || ''
    // selectedFolder 会通过 watch selectedFolderId 自动更新
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ i18n.t('share.title') }}</DialogTitle>
        <DialogDescription>
          {{ i18n.t('share.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 文件夹选择器 -->
        <div class="space-y-2">
          <div v-if="selectedFolderId" class="rounded-lg border p-3 bg-muted/50">
            <div class="flex items-center gap-2">
              <Icon name="lucide:folder" class="h-4 w-4 text-muted-foreground" />
              <FolderBreadcrumbs :folderId="selectedFolderId" :maxCrumbs="3" class="[&_svg]:size-2.5!" />
            </div>
            <div class="text-xs text-muted-foreground mt-1">
              {{ selectedFolder?.children?.length || 0 }} items
            </div>
          </div>
          <FolderSeletor
            v-else
            :currentFolderId="selectedFolderId"
            @folderSelected="handleFolderSelect"
          />
        </div>

        <!-- 公开设置 -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <div class="space-y-0.5">
              <Label>{{ i18n.t('share.publicToggle') }}</Label>
              <div class="text-sm text-muted-foreground">
                {{ isPublic ? i18n.t('share.publicDescription') : i18n.t('share.privateDescription') }}
              </div>
            </div>
            <Switch
              :checked="isPublic"
              @update:checked="(value: boolean) => isPublic = value"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            @click="emit('openChange', false)"
          >
            {{ i18n.t('dialog.action.cancel') }}
          </Button>
          <Button
            type="button"
            :disabled="loading || !selectedFolderId"
            @click="onSubmit"
          >
            <Icon v-if="loading" name="lucide:loader-2" class="mr-2 h-4 w-4 animate-spin" />
            {{ i18n.t('share.createShare') }}
          </Button>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>
