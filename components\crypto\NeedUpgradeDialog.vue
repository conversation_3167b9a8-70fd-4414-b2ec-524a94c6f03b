<script setup lang="ts">
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'
defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
}>()

const handleUpgrade = () => {
  // TODO: 实现升级逻辑，比如跳转到升级页面
  window.open('https://your-upgrade-url.com', '_blank')
  emit('openChange', false)
}
</script>

<template>
  <Dialog :open="open" @update:open="$emit('openChange', $event)">
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>{{ i18n.t('dialog.needUpgrade.title') || 'Upgrade to Pro' }}</DialogTitle>
        <DialogDescription>
          {{ i18n.t('dialog.needUpgrade.description') || 'You have reached the limit for free users. Upgrade to Pro to unlock unlimited encrypted bookmarks and more features.' }}
        </DialogDescription>
      </DialogHeader>

      <div class="flex flex-col space-y-4 py-4">
        <div class="flex items-center space-x-2">
          <Icon name="lucide:check" class="h-4 w-4 text-green-500" />
          <span class="text-sm">{{ i18n.t('dialog.needUpgrade.feature1') || 'Unlimited encrypted bookmarks' }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <Icon name="lucide:check" class="h-4 w-4 text-green-500" />
          <span class="text-sm">{{ i18n.t('dialog.needUpgrade.feature2') || 'Advanced bookmark organization' }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <Icon name="lucide:check" class="h-4 w-4 text-green-500" />
          <span class="text-sm">{{ i18n.t('dialog.needUpgrade.feature3') || 'Priority support' }}</span>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="$emit('openChange', false)">
          {{ i18n.t('dialog.needUpgrade.cancel') || 'Maybe Later' }}
        </Button>
        <Button @click="handleUpgrade">
          {{ i18n.t('dialog.needUpgrade.upgrade') || 'Upgrade to Pro' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

