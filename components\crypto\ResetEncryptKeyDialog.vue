<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { useCrypto } from '@/composables/useCrypto'
import { Dialog, DialogTitle, DialogContent, DialogHeader, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

const { resetUserEncryptKey } = useCrypto()

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [bookmark: object | null]
}>()

const schema = toTypedSchema(
  z.object({
    masterPassword: z.string().min(4),
  }),
)
const form = useForm({
  validationSchema: schema,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    await resetUserEncryptKey(values.masterPassword)
    emit('openChange', false)
  }
  catch (error) {
    toast.error(i18n.t('error.unknown'))
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Reset PIN code</DialogTitle>
        <DialogDescription>
          Please set your PIN code before using encrypted bookmarks.
        </DialogDescription>
      </DialogHeader>

      <form
        id="dialogForm"
        class="flex flex-col items-stretch gap-4"
        :validation-schema="schema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="masterPassword"
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="PIN code"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <Button
          type="submit"
          form="dialogForm"
        >
          Done
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
