import { authClient } from '@/auth/auth-client'

export interface User {
  id: string
  email: string
  name: string
  avatarUrl: string
  plan?: string
  role?: string
  encryptSalt: string
  encryptKey: string
  encryptIv: string
  encryptAlgo: string
}

export interface UserSessionComposable {
  /**
   * The user object if logged in, null otherwise.
   */
  user: ComputedRef<User | null>
  /**
   * Fetch the user session from the server.
   */
  refetch: () => Promise<void>
  /**
   * Clear the user session and remove the session cookie.
   */
  clear: () => Promise<void>
}

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息
  const user = computed(() => session.value?.data?.user)

  // 清除会话（登出）
  const clear = async () => {
    await authClient.signOut()
  }

  return {
    // 会话状态
    loggedIn: readonly(loggedIn),
    user: readonly(user),

    // 方法 - 直接使用 session 对象的方法
    refetch: (session as any).refetch,
    clear,

    // 原始会话对象
    session
  }
}

// 导出默认函数以保持兼容性
export default useUserSession

