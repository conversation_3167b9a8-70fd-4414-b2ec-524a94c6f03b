export interface User {
  id: string
  email: string
  name: string
  avatarUrl: string
  plan?: string
  role?: string
  encryptSalt: string
  encryptKey: string
  encryptIv: string
  encryptAlgo: string
}

export interface UserSessionComposable {
  /**
   * The user object if logged in, null otherwise.
   */
  currentUser: ComputedRef<User | null>
  /**
   * Fetch the user session from the server.
   */
  refreshSession: () => Promise<void>
  /**
   * Clear the user session and remove the session cookie.
   */
  clear: () => Promise<void>
}

/**
 * Composable to get back the user session and utils around it.
 * https://github.com/atinux/nuxt-auth-utils/blob/ec9b7279469a8e6f6e92530f9d5640b589f727a4/src/runtime/app/composables/session.ts
 */
// 当 gomark.pro 上登录后，nuxt-session 已经在 gomark.pro 的 cookie 中
// 而扩展设置了 host_permissions，因此所有请求中都会自动带上 gomark.pro 的 cookie，也就是身份鉴权

// 网页也可以直接向扩展发消息，未验证
// https://developer.chrome.com/docs/extensions/develop/concepts/messaging?hl=zh-cn#external-webpage
// chrome 对象在网页中是全局可用的，但只有在用户安装了你的扩展并且扩展配置了 externally_connectable 时，chrome.runtime 才会可用。
const endpoint = import.meta.env.VITE_ENDPOINT

// 模块级单例模式
let isInitialized = false
const _currentUser = ref<any | null>(null)

// 初始化逻辑（只会执行一次）
const initializeUserSession = () => {
  if (isInitialized) return
  
  // 读取初始值
  chrome.storage.local.get(['user'], (result) => {
    _currentUser.value = result.user || null
  })

  // 设置监听（只会注册一次）
  chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'local' && changes.user) {
      _currentUser.value = changes.user.newValue
    }
  })

  isInitialized = true
}

export default function useUserSession(): UserSessionComposable {
  // 从 chrome.storage.local 中读取 user，如果不存在则使用默认值，从而实现登录持久化，不用每次加载时都发 refreshSession
  // 网页通知扩展主要有两种方式
  // 1. window.postMessage
  // 2. chrome.runtime.sendMessage - 需要配置 externally_connectable, localhost 也得配置
  // WEB 上登录或登出时 Nuxt 端的 app.vue 中会 watch user.value，并发出 window.postMessage 通知
  // 扩展端 content.ts 设置监听 website_to_ext 事件，收到后会执行 refreshSession = 发请求到服务器
  // 因为已经设置了 host_permissions，所以请求会带上 gomark.pro 的 cookie, 用户实际上已经登录, refreshSession 主要目的是更新 chrome.storage.sync 中的 user

  initializeUserSession()

  const clear = async () => {
    const response = await fetch(`${endpoint}/api/_auth/session`, {
      method: 'DELETE',
    });

    if (response.ok) {
      chrome.storage.local.set({ user: null });
    }
  };

  const refreshSession = async () => {
    console.log('--------- refreshSession start ---------');
    const response = await fetch(`${endpoint}/api/_auth/session`, {
      headers: {
        accept: 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      chrome.storage.local.set({ 'user': data?.user || null });
    } else {
      chrome.storage.local.set({ 'user': null });
    }
  };
  return {
    currentUser: computed(() => _currentUser.value),
    refreshSession,
    clear
  };
}