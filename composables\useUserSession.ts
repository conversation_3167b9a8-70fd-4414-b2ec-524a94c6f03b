import { authClient } from '@/utils/auth-client'

// 扩展 better-auth 的用户类型，添加我们需要的字段
export interface ExtendedUser {
  id: string
  email: string
  name: string
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  image?: string | null
  // 扩展字段
  avatarUrl?: string
  plan?: string
  role?: string
  encryptSalt?: string
  encryptKey?: string
  encryptIv?: string
  encryptAlgo?: string
}

// 保持向后兼容
export type User = ExtendedUser

export interface UserSessionComposable {
  /**
   * The user object if logged in, null otherwise.
   */
  user: ComputedRef<User | null>
  /**
   * Fetch the user session from the server.
   */
  refetch: () => Promise<void>
  /**
   * Clear the user session and remove the session cookie.
   */
  clear: () => Promise<void>
}

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息 - 将 better-auth 的用户对象转换为我们的扩展类型
  const user = computed(() => {
    const betterAuthUser = session.value?.data?.user
    if (!betterAuthUser) return null

    // 将 better-auth 用户对象转换为我们的扩展类型
    return {
      ...betterAuthUser,
      // 映射字段
      avatarUrl: betterAuthUser.image,
      // 这些字段可能来自服务端的额外数据，暂时设为可选
      plan: (betterAuthUser as any).plan,
      role: (betterAuthUser as any).role,
      encryptSalt: (betterAuthUser as any).encryptSalt,
      encryptKey: (betterAuthUser as any).encryptKey,
      encryptIv: (betterAuthUser as any).encryptIv,
      encryptAlgo: (betterAuthUser as any).encryptAlgo,
    } as ExtendedUser
  })

  // 清除会话（登出）
  const clear = async () => {
    await authClient.signOut()
  }

  return {
    // 会话状态
    loggedIn: readonly(loggedIn),
    user: readonly(user),

    // 方法 - 直接使用 session 对象的方法
    refetch: (session as any).refetch,
    clear,

    // 原始会话对象
    session
  }
}

// 导出默认函数以保持兼容性
export default useUserSession

