<script setup lang="ts">
import { Button } from '@/components/ui/button';
import type { RxDatabase } from 'rxdb'
import cuid from 'cuid';
import { useFileDialog } from '@vueuse/core'

import tagdownData from '../../../data/tagdown_1735406525655.json';

const { user } = useUserSession()
const db = inject<RxDatabase>('db')!

// 使用 VueUse 的 useFileDialog 来处理文件选择
const { files, open, reset } = useFileDialog({
  accept: '.json',
  multiple: false,
})

// 监听文件变化
watch(files, async (newFiles) => {
  if (newFiles && newFiles.length > 0) {
    await importDataFromFile(newFiles[0])
    // 重置文件选择
    reset()
  }
})

// 获取所有唯一标签
function getAllUniqueTags() {
  const tags = new Set<string>();
  tagdownData.data.data[0].rows.forEach((bookmark: any) => {
    bookmark.tags?.forEach((tag: string) => tags.add(tag));
  });
  return Array.from(tags);
}

// 根据URL查找书签ID
async function findBookmarkIdByUrl(url: string): Promise<string | null> {
  return new Promise(resolve => {
    chrome.bookmarks.search({ url }, results => {
      resolve(results[0]?.id || null);
    });
  });
}

async function importTagsFromTagdown() {
  console.warn('--------- importTagsFromTagdown ---------');
  // 1. 创建所有标签
  const uniqueTags = getAllUniqueTags();
  const browserAccount = await browser.identity.getProfileUserInfo()
  
  // 批量创建标签
  await Promise.all(uniqueTags.map(async tagName => {
    const existing = await db.tags.findOne({
      selector: { name: tagName }
    }).exec();
    
    if (!existing) {
      await db.tags.insert({
        id: cuid(),
        name: tagName,
        userId: user.value?.id ?? null,
        browserAccountId: browserAccount.id,
        bookmarkIds: [],
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });
    }
  }));

  // 2. 关联书签ID
  const allBookmarks = tagdownData.data.data[0].rows;
  
  for (const bookmark of allBookmarks as Array<{ 
    url: string; 
    tags?: string[]; 
    // 其他可能需要的属性...
  }>) {
    const bookmarkId = await findBookmarkIdByUrl(bookmark.url);
    if (!bookmarkId || !bookmark.tags) continue;

    // 为每个标签添加书签ID
    await Promise.all(bookmark.tags.map(async tagName => {
      const tagDoc = await db.tags.findOne({
        selector: { name: tagName }
      }).exec();

      if (tagDoc && !tagDoc.bookmarkIds.includes(bookmarkId)) {
        await tagDoc.update({
          $push: { bookmarkIds: bookmarkId }
        });
      }
    }));
  }
}

/**
 * 导出数据库中的 extras 和 tags 表
 */
async function exportData() {
  try {
    // 获取所有 extras 数据
    const extrasData = await db.extras.find().exec();
    
    // 获取所有 tags 数据
    const tagsData = await db.tags.find().exec();
    
    // 创建导出对象
    const exportData = {
      extras: extrasData.map(doc => doc.toJSON()),
      tags: tagsData.map(doc => doc.toJSON()),
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
    
    // 转换为 JSON
    const jsonString = JSON.stringify(exportData, null, 2);
    
    // 使用当前日期作为文件名
    const fileName = `rxdb-export-${new Date().getTime()}.json`;
    
    // 创建下载链接并触发下载
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(url);
    
    console.log('数据导出成功');
  } catch (error) {
    console.error('导出数据时出错:', error);
  }
}

/**
 * 从文件导入数据
 */
async function importDataFromFile(file: File) {
  try {
    const fileContent = await file.text();
    const importData = JSON.parse(fileContent);
    
    const browserAccount = await browser.identity.getProfileUserInfo();
    
    // 导入 extras 数据
    if (importData.extras && Array.isArray(importData.extras)) {
      for (const extraData of importData.extras) {
        const existing = await db.extras.findOne({
          selector: { bookmarkId: extraData.bookmarkId }
        }).exec();
        
        if (!existing) {
          await db.extras.insert({
            id: cuid(),
            bookmarkId: extraData.bookmarkId,
            userId: user.value?.id ?? null,
            browserAccountId: browserAccount.id,
            notes: extraData.notes || '',
            emoji: extraData.emoji || '',
            updatedAt: new Date().toISOString(),
            createdAt: new Date().toISOString()
          });
        }
      }
    }
    
    // 导入 tags 数据
    if (importData.tags && Array.isArray(importData.tags)) {
      for (const tagData of importData.tags) {
        const existing = await db.tags.findOne({
          selector: { name: tagData.name }
        }).exec();
        
        if (!existing) {
          await db.tags.insert({
            id: cuid(),
            name: tagData.name,
            userId: user.value?.id,
            browserAccountId: browserAccount.id,
            bookmarkIds: tagData.bookmarkIds || [],
            updatedAt: new Date().toISOString(),
            createdAt: new Date().toISOString()
          });
        } else {
          // 合并书签ID，避免重复
          const mergedBookmarkIds = [...new Set([
            ...existing.bookmarkIds,
            ...(tagData.bookmarkIds || [])
          ])];
          
          await existing.update({
            $set: { bookmarkIds: mergedBookmarkIds }
          });
        }
      }
    }
    
    console.log('数据导入成功');
  } catch (error) {
    console.error('导入数据时出错:', error);
  }
}

</script>

<template>
  <div class="flex flex-col gap-4">
    <div class="flex gap-2">
      <Button variant="outline" @click="importTagsFromTagdown">开发导入</Button>
      <Button variant="outline" @click="exportData">导出数据</Button>
      <Button variant="outline" @click="open">导入数据</Button>
    </div>
  </div>
</template>
