/**
 * 导出所有 IndexedDB 数据库和对象存储的数据
 * 直接在 Console 中执行即可，无需安装任何扩展
 * @returns {Promise<void>}
 */
async function exportAllIndexedDBData() {
  try {
    // 1. 获取所有数据库名称
    const databases = await indexedDB.databases();
    if (!databases || databases.length === 0) {
      console.log("未找到 IndexedDB 数据库");
      return;
    }

    // 2. 遍历每个数据库，导出数据
    const allData = {};
    for (const dbInfo of databases) {
      const dbName = dbInfo.name;
      try {
        const dbData = await new Promise((resolve, reject) => {
          const request = indexedDB.open(dbName);
          request.onsuccess = (event) => {
            const db = event.target.result;
            const storeNames = Array.from(db.objectStoreNames);
            const dbExport = {};

            // 遍历所有对象存储
            const promises = storeNames.map((storeName) => {
              return new Promise((storeResolve) => {
                const transaction = db.transaction(storeName, "readonly");
                const store = transaction.objectStore(storeName);
                const getAllRequest = store.getAll();

                getAllRequest.onsuccess = () => {
                  dbExport[storeName] = getAllRequest.result;
                  storeResolve();
                };
                getAllRequest.onerror = (error) => {
                  console.error(`导出对象存储 ${storeName} 失败:`, error);
                  storeResolve();
                };
              });
            });

            // 等待所有对象存储导出完成
            Promise.all(promises).then(() => {
              resolve(dbExport);
              db.close();
            });
          };
          request.onerror = (error) => {
            reject(`打开数据库 ${dbName} 失败: ${error}`);
          };
        });

        allData[dbName] = dbData;
        console.log(`数据库 ${dbName} 导出完成`);
      } catch (error) {
        console.error(`处理数据库 ${dbName} 时出错:`, error);
      }
    }

    // 3. 生成下载链接
    const blob = new Blob([JSON.stringify(allData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `indexeddb-export-${new Date().toISOString()}.json`;
    a.click();
    console.log("所有 IndexedDB 数据已导出并下载！");
  } catch (error) {
    console.error("导出过程中出错:", error);
  }
}
// 执行导出
await exportAllIndexedDBData();


/**
 * 从导出的 JSON 文件导入数据到目标扩展的 IndexedDB
 * 直接在 Console 中执行即可，无需安装任何扩展
 * @param {Object} importedData - 导出的 JSON 数据（格式为 { dbName: { storeName: [data] } }）
 * @returns {Promise<void>}
 */
async function importIndexedDBData(importedData) {
  try {
    for (const dbName in importedData) {
      if (!importedData.hasOwnProperty(dbName)) continue;

      const dbStores = importedData[dbName];
      const request = indexedDB.open(dbName);

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        for (const storeName in dbStores) {
          if (!db.objectStoreNames.contains(storeName)) {
            db.createObjectStore(storeName, { keyPath: "id" }); // 根据实际结构调整 keyPath
          }
        }
      };

      await new Promise((resolve) => {
        request.onsuccess = (event) => {
          const db = event.target.result;
          const storeNames = Object.keys(dbStores);

          const promises = storeNames.map((storeName) => {
            return new Promise((storeResolve) => {
              const transaction = db.transaction(storeName, "readwrite");
              const store = transaction.objectStore(storeName);
              const data = dbStores[storeName];

              // 清空现有数据（可选）
              const clearRequest = store.clear();
              clearRequest.onsuccess = () => {
                // 批量插入新数据
                data.forEach((item) => {
                  store.put(item);
                });
                storeResolve();
              };
            });
          });

          Promise.all(promises).then(() => {
            db.close();
            resolve();
          });
        };
      });
      console.log(`数据库 ${dbName} 导入完成`);
    }
    console.log("所有数据导入成功！");
  } catch (error) {
    console.error("导入过程中出错:", error);
  }
}

// 用法示例（粘贴导出的 JSON 数据）
const exportedData = {
  "myDatabase1": {
    "users": [{ "id": 1, "name": "Alice" }],
    "settings": [{ "theme": "dark" }]
  },
  "myDatabase2": {
    "logs": [{ "timestamp": "2023-01-01", "event": "click" }]
  }
};
await importIndexedDBData(exportedData);