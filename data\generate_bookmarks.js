const fs = require('fs');
const path = require('path');

// 函数用于生成单个bookmark对象
function createBookmark(index) {
  return {
    id: `bookmark_${index}`,
    parentId: 'root', // 假设所有书签都在根目录下
    index: index,
    url: `https://example.com/${index}`,
    title: `Bookmark ${index}`
  };
}

// 函数用于生成1万个bookmarks数组
function generateBookmarks() {
  const bookmarks = [];
  for (let i = 0; i < 100000; i++) {
    bookmarks.push(createBookmark(i));
  }
  return bookmarks;
}

// 函数用于将bookmarks数组保存为JSON文件
function saveBookmarksToJson(bookmarks, filename) {
  const jsonContent = JSON.stringify(bookmarks, null, 2);
  fs.writeFileSync(filename, jsonContent, 'utf8');
}

// 使用函数生成bookmarks并保存为JSON文件
const bookmarks = generateBookmarks();
saveBookmarksToJson(bookmarks, path.join(__dirname, 'bookmarks.json'));