const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 加载之前生成的bookmarks.json文件
const bookmarksFilePath = path.join(__dirname, 'bookmarks_10K.json');
let bookmarks;
try {
  bookmarks = JSON.parse(fs.readFileSync(bookmarksFilePath, 'utf8'));
} catch (error) {
  console.error('Error reading bookmarks.json file:', error);
  process.exit(1);
}

// 函数用于生成随机字符串
function generateRandomString(length) {
  return crypto.randomBytes(length).toString('hex').slice(0, length);
}

// 函数用于生成单个tag对象
function createTag() {
  let randomString = generateRandomString(10)
  return {
    id: randomString,
    name: `Tag ${randomString.slice(-5)}`,
    bookmark_ids: []
  };
}

// 函数用于生成随机整数
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 函数用于生成1000个tags数组
function generateTags(bookmarks) {
  const tags = [];
  for (let i = 0; i < 1000; i++) {
    const tag = createTag();
    // 随机选择10-1000个bookmark_id填充到bookmark_ids数组
    const numberOfBookmarks = getRandomInt(10, 100); // 更随机的范围
    for (let j = 0; j < numberOfBookmarks; j++) {
      tag.bookmark_ids.push(bookmarks[Math.floor(Math.random() * bookmarks.length)].id);
    }
    tags.push(tag);
  }
  return tags;
}

// 函数用于将tags数组保存为JSON文件
function saveTagsToJson(tags, filename) {
  const jsonContent = JSON.stringify(tags, null, 2);
  fs.writeFileSync(filename, jsonContent, 'utf8');
}

// 使用函数生成tags并保存为JSON文件
const tags = generateTags(bookmarks);
saveTagsToJson(tags, path.join(__dirname, 'tags.json'));