import { initRxDB, updateNullUserIdsToCurrentUser } from '@/rxdb';
import { runRxDBReplication, cancelRxDBReplication, deleteRxDBReplication} from '@/rxdb/sync'
const { user, refetch } = useUserSession()
import { openSSE, closeSSE } from '@/utils/sse'
// import { setupRxDB } from './setupRxDB';
import { migrateData } from '@/rxdb/migration';
import { setupBookmarks } from './setupBookmarks';
import { setupContextMenus } from './setupContextMenus';
import { setupActionIcon } from './setupActionIcon';
import { setupCrypto } from './setupCrypto';
// import { setupSnapshot } from './setupSnapshot';

// 注意，background / bookmarks / popup 之间的上下文是隔离的
// 所以，如果需要共享 rxdb 实例，需要使用 chrome.runtime.onMessage 来传递消息
// 不做共享 rxdb 实例的方案，rxdb 允许多实例
// 也不做 crypto 后台共享实例的方案，因为是纯端对端加密，需要用户输入密码，做多上下文共享没有意义
export default defineBackground(() => {
  (async () => {
    try {
      // 初始化 rxdb, 获取或者创建一个 rxdb 实例
      await initRxDB();
      await migrateData();
      
      // 添加登录状态监听，watch 在这里作为副作用触发器，仍然是必要的
      watch(user, async (newUser, oldUser) => {
        console.warn('background - user changed - ', newUser, oldUser)
        // 当用户ID未变化时跳过
        if (newUser?.id === oldUser?.id) return;

        if (newUser) {
          await openSSE();
          await updateNullUserIdsToCurrentUser(newUser.id);
          await runRxDBReplication();
          // 初始化快照功能
          // setupSnapshot();
        } else {
          await closeSSE();
          await cancelRxDBReplication();
        }
      }, { immediate: true }); // 立即执行一次以处理初始状态

      refetch();
      const account = await browser.identity.getProfileUserInfo()
      console.warn('background -------- browser account', account)
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
    }
  })();

  // setupRxDB();
  setupBookmarks();
  setupContextMenus();
  setupActionIcon();
  setupCrypto();
});

