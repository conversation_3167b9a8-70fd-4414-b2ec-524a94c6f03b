// Chrome 书签 API 的事件缓存机制：
// Chrome 书签 API 会缓存最近的书签变更事件
// 当新的监听器被添加时（例如 popup 重新打开），Chrome 会重放这些事件
// 每次 popup 打开都会重新注册监听器
// 每次 popup 打开，background script 中的 onCreated 监听器会收到 Chrome 重放的事件
// 这就是为什么你看到了之前创建的文件夹的 onCreated 事件

export async function setupBookmarks() {  
  // 在开始书签导入会话时触发。在 onImportEnded 触发之前，开销较大的观察器应忽略 onCreated 更新。观察器仍应立即处理其他通知。
  let onImport = false
  browser.bookmarks.onImportBegan.addListener(() => {
    onImport = true
    console.warn('chrome.bookmarks.onImportBegan ! ')
  });

  browser.bookmarks.onImportEnded.addListener(() => {
    onImport = false
    console.warn('chrome.bookmarks.onImportEnded ! ')
    browser.runtime.sendMessage({
      type: 'bookmark-change',
      action: 'onImportEnded',
      folder: []
    })
  });

  // 在书签或文件夹发生变化时触发。注意：目前，只有标题和网址更改会触发此操作
  browser.bookmarks.onChanged.addListener((id, changeInfo) => {
    console.warn('chrome.bookmarks.onChanged - ', id, changeInfo);
    browser.runtime.sendMessage({
      type: 'bookmark-change',
      action: 'onChanged',
      folder: [id]
    });
  });

  // 当文件夹的子项因界面中的排序而更改顺序时触发。此方法不会因 move() 而被调用。
  browser.bookmarks.onChildrenReordered.addListener((id, reorderInfo) => {
    console.warn('chrome.bookmarks.onChildrenReordered - ', id, reorderInfo)
    browser.runtime.sendMessage({
      type: 'bookmark-change',
      action: 'onChildrenReordered',
      folder: [id]
    })
  });
  
  // 在创建书签或文件夹时触发。
  browser.bookmarks.onCreated.addListener((id, bookmarkNode) => {
    if (!onImport) {
      console.warn('chrome.bookmarks.onCreated - ', id, bookmarkNode);
      browser.runtime.sendMessage({
        type: 'bookmark-change',
        action: 'onCreated',
        folder: [bookmarkNode.url ? bookmarkNode.parentId : id ]
      });
    }
  });

  // 当书签或文件夹移至其他父级文件夹时触发。
  browser.bookmarks.onMoved.addListener((id, moveInfo) => {
    console.warn('chrome.bookmarks.onMoved - ', id, moveInfo)
    browser.runtime.sendMessage({
      type: 'bookmark-change',
      action: 'onMoved',
      folder: [id, moveInfo.parentId, moveInfo.oldParentId]
    })
  });

  // 在移除书签或文件夹时触发。递归移除文件夹时，系统会为该文件夹触发一次通知，而不会为其内容触发任何通知。
  browser.bookmarks.onRemoved.addListener((id, removeInfo) => {
    console.warn('chrome.bookmarks.onRemoved - ', id, removeInfo);
    browser.runtime.sendMessage({
      type: 'bookmark-change',
      nodeType: removeInfo.node.url ? 'bookmark' : 'folder',
      action: 'onRemoved',
      folder: [id, removeInfo.parentId]
    });
  });
}