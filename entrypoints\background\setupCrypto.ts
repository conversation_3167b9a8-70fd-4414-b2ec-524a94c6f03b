// Background script 中的加密状态管理
// 负责自动锁定、跨上下文状态同步等

const STORAGE_KEY = 'encryption_info';
const LOCK_TIMEOUT = 5 * 60 * 1000; // 5分钟
const CHECK_INTERVAL = 30 * 1000; // 每30秒检查一次

let checkTimer: NodeJS.Timeout | null = null;

// 获取存储的加密数据
const getStoredEncryptionData = async () => {
  const result = await chrome.storage.local.get(STORAGE_KEY);
  return result[STORAGE_KEY] || null;
};

// 清除存储的加密数据
const clearStoredEncryptionData = async () => {
  await chrome.storage.local.remove(STORAGE_KEY);
  console.log('Background: cleared encryption storage');
};

// 检查是否应该自动锁定
const checkAutoLock = async () => {
  try {
    const stored = await getStoredEncryptionData();
    if (!stored) {
      return; // 没有存储数据，无需检查
    }

    const timeSinceLastActivity = Date.now() - stored.lastActivity;
    console.log('Background: checkAutoLock', {
      timeSinceLastActivity,
      lockTimeout: LOCK_TIMEOUT,
      shouldLock: timeSinceLastActivity >= LOCK_TIMEOUT
    });

    if (timeSinceLastActivity >= LOCK_TIMEOUT) {
      console.log('Background: auto-locking due to inactivity');
      await clearStoredEncryptionData();
      // 存储变化会自动通知所有监听的上下文
    }
  } catch (error) {
    console.error('Background: error in checkAutoLock:', error);
  }
};



// 启动定期检查
const startPeriodicCheck = () => {
  if (checkTimer) {
    clearInterval(checkTimer);
  }

  checkTimer = setInterval(checkAutoLock, CHECK_INTERVAL);
  console.log('Background: started periodic auto-lock check');
};

// 停止定期检查
const stopPeriodicCheck = () => {
  if (checkTimer) {
    clearInterval(checkTimer);
    checkTimer = null;
    console.log('Background: stopped periodic auto-lock check');
  }
};

// 设置加密管理
export const setupCrypto = () => {
  console.log('Background: setting up crypto management');

  // 扩展启动时检查一次
  checkAutoLock();

  // 监听存储变化
  chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'local' && changes[STORAGE_KEY]) {
      const newValue = changes[STORAGE_KEY].newValue;
      const oldValue = changes[STORAGE_KEY].oldValue;

      console.log('Background: encryption storage changed', {
        newValue: !!newValue,
        oldValue: !!oldValue
      });

      if (newValue && !oldValue) {
        // 加密数据被创建，开始检查
        startPeriodicCheck();
      } else if (!newValue && oldValue) {
        // 加密数据被清除，停止检查
        stopPeriodicCheck();
      }
    }
  });
};
