// 当已共享的书签发生变化时触发，将完整数据更新到 shared 中
export async function setupShared() {  
  // browser.bookmarks.onChanged.addListener((id, changeInfo) => {
  //   console.warn('chrome.bookmarks.onChanged - ', id, changeInfo);
  // });

  // browser.bookmarks.onCreated.addListener((id, bookmarkNode) => {
  //   console.warn('chrome.bookmarks.onCreated - ', id, bookmarkNode);
  // });

  // browser.bookmarks.onRemoved.addListener((id, removeInfo) => {
  //   console.warn('chrome.bookmarks.onRemoved - ', id, removeInfo);
  // });
}