import cuid from 'cuid';
import { hashString } from '@/utils'

const { currentUser } = useUserSession();
const endpoint = import.meta.env.VITE_ENDPOINT;
const currentBrowser = import.meta.env.BROWSER;
const browserAccount = await browser.identity.getProfileUserInfo()

// 当用户开启快照功能时，按每天一次的频率将快照数据存储到服务器中
export async function setupSnapshot() {
  if (!currentUser.value?.id) return;
  
  const today = new Date();
  
  // 检查今天是否已创建快照
  const snapshots = await getAllSnapshots();
  const lastSnapshot = snapshots.length > 0 ? snapshots[0] : null;
  
  if (shouldCreateSnapshot(lastSnapshot, today)) {
    await createSnapshot(today);
    await cleanupOldSnapshots(snapshots);
  }
}

async function getAllSnapshots(): Promise<Array<{ id: string, createdAt: string, source: string, browserAccountId: string, userId: string | null }>> {
  try {
    // 构建查询参数
    const params = new URLSearchParams({
      source: currentBrowser,
      browserAccountId: browserAccount.id
    });
    
    // 从服务端获取所有快照记录，默认按时间降序排列
    const response = await fetch(`${endpoint}/api/snapshots?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      console.error('获取快照列表失败:', response.statusText);
      return [];
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取快照列表时出错:', error);
    return [];
  }
}

function shouldCreateSnapshot(lastSnapshot: { createdAt: string } | null, today: Date): boolean {
  if (!lastSnapshot) return true;
  
  const lastDate = new Date(lastSnapshot.createdAt);
  
  // 如果今天已创建，则不再创建
  if (lastDate.toISOString().split('T')[0] === today.toISOString().split('T')[0]) {
    return false;
  }
  
  // 是否是月末（明天是新的一个月）
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  const isMonthEnd = tomorrow.getMonth() !== today.getMonth();
  
  // 如果是最近3天或月末，则创建快照
  const daysDiff = Math.floor((today.getTime() - lastDate.getTime()) / (24 * 60 * 60 * 1000));
  return daysDiff <= 3 || isMonthEnd;
}

async function createSnapshot(date: Date): Promise<void> {
  try {
    // 获取所有书签
    const bookmarkTree = await browser.bookmarks.getTree();
    
    // 发送快照数据到服务端
    const response = await fetch(`${endpoint}/api/snapshots`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        id: cuid(),
        source: currentBrowser,
        browserAccountId: browserAccount.id,
        userId: currentUser.value?.id ?? null,
        bookmarkData: JSON.stringify(bookmarkTree),
        updatedAt: date.toISOString(),
        createdAt: date.toISOString()
      })
    });
    
    if (!response.ok) {
      throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
    }
    
    console.log('书签快照已创建:', date.toISOString().split('T')[0]);
  } catch (error) {
    console.error('创建书签快照失败:', error);
  }
}

async function cleanupOldSnapshots(snapshots: Array<{ id: string, createdAt: string }>): Promise<void> {
  try {
    // 保留的快照：最近3天的和每月最后一天的
    const snapshotsToKeep = new Set<string>();
    
    // 最近3次的快照
    for (let i = 0; i < Math.min(3, snapshots.length); i++) {
      snapshotsToKeep.add(snapshots[i].id);
    }
    
    // 每月最后一天的快照
    const monthlySnapshots = new Map<string, { id: string, createdAt: string }>();
    snapshots.forEach(snapshot => {
      const date = new Date(snapshot.createdAt);
      const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
      
      if (!monthlySnapshots.has(monthKey) || 
          new Date(monthlySnapshots.get(monthKey)!.createdAt).getDate() < date.getDate()) {
        monthlySnapshots.set(monthKey, snapshot);
      }
    });
    
    monthlySnapshots.forEach(snapshot => {
      snapshotsToKeep.add(snapshot.id);
    });
    
    // 删除不需要保留的快照
    const snapshotsToDelete = snapshots.filter(snapshot => !snapshotsToKeep.has(snapshot.id));
    
    for (const snapshot of snapshotsToDelete) {
      await fetch(`${endpoint}/api/snapshots/${snapshot.id}`, {
        method: 'DELETE',
        credentials: 'include'
      });
    }
    
    console.log(`已清理 ${snapshotsToDelete.length} 个旧快照`);
  } catch (error) {
    console.error('清理旧快照失败:', error);
  }
}
