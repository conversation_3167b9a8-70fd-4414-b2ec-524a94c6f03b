const { refreshSession } = useUserSession()
const match = import.meta.env.DEV ? 'http://localhost/*' : `${import.meta.env.VITE_WEBSITE}/*`

export default defineContentScript({
  matches: [match],
  main() {
    window.addEventListener('message', (event) => {
      // console.log('ContentScript 收到来自网站的事件', event)
      if (event.data.type === 'website_to_ext') {
        if (event.data.action === 'userChanged') {
          refreshSession();
        }
      }
    });
  },
});
