<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { useColorMode } from '@vueuse/core'
import { TooltipProvider } from  '@/components/ui/tooltip'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import List from './List.vue';
import FolderTree from './components/sidebar/FolderTree.vue';
import EncryptedFolderTree from './components/sidebar/EncryptedFolderTree.vue';
import Header from './components/Header.vue';
import { Toaster } from '@/components/ui/sonner'
import UserDropdown from './components/UserDropdown.vue';
import Icon from '@/components/Icon.vue';
import { Button } from '@/components/ui/button';
import SetEncryptKeyDialog from '@/components/crypto/SetEncryptKeyDialog.vue'
import ResetEncryptKeyDialog from '@/components/crypto/ResetEncryptKeyDialog.vue'
import UnlockEncryptDialog from '@/components/crypto/UnlockEncryptDialog.vue'
import NeedUpgradeDialog from '@/components/crypto/NeedUpgradeDialog.vue'
import NewEncryptedFolderDialog from './components/encrypted/NewFolderDialog.vue'
import NeedToLoginDialog from '@/components/NeedToLogin.vue'
import ShareFolderDialog from '@/components/ShareFolderDialog.vue'
import { useDialog } from '@/composables/useDialog';
import { useCrypto } from '@/composables/useCrypto';
import { rxDBInstance, initRxDB } from '@/rxdb/index'
import { useVisibleFields } from '@/composables/useVisibleFields'

const crypto = useCrypto();

const { dialogs, registerDialog, openDialog, closeDialog, toggleDialog } = useDialog();
registerDialog('setEncryptKey');
registerDialog('resetEncryptKey');
registerDialog('unlockEncrypt');
registerDialog('newEncryptedFolder');
registerDialog('needToLogin');
registerDialog('needUpgrade');
registerDialog('shareFolder');

const route = useRoute();
const router = useRouter();
const currentFolderId = ref('')  // 初始值直接使用 route.query.id
watch(
  () => route.query.id,
  async (newId) => {
    currentFolderId.value = (newId as string) || '1';
  }, { immediate: true }
);

// 写一个函数设置 currentFolderId
function setSelectedFolderId(id: string) {
  // 改为改变当前路径的 id 参数，不改变路径
  router.replace({ query: { ...route.query, id } })
}
provide('setSelectedFolderId', setSelectedFolderId)

// 默认各分栏窗口宽度百分比
const defaultLayout = ref([2, 98])

const isCollapsed = ref(true)
const navCollapsedSize = ref(4)

function onCollapse() {
  isCollapsed.value = true
}

function onExpand() {
  isCollapsed.value = false
}

const colorMode = useColorMode();
// 从 chrome.storage.sync 中获取 ColorMode
browser.storage.sync.get(['colorMode'], (result) => {
  colorMode.value = result.colorMode || 'light'; // 默认值为 'dark'
});
// 监听 chrome.storage.sync 中 colorMode 的变化
browser.storage.onChanged.addListener((changes) => {
  if (changes.colorMode) {
    colorMode.value = changes.colorMode.newValue; // 更新页面的 colorMode
  }
});

// 列表可见字段
const { visibleFields } = useVisibleFields()

onMounted(async () => {
  // router.isReady() 之后才能获取到 route.query.id，避免二次刷新 List 组件
  await initRxDB();
  // analytics.setEnabled(true);
  // analytics.autoTrack(document);
});
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <Toaster />
    <div class="flex flex-col">
      <Header />
      <div class="h-[calc(100vh-60px)]">
        <ResizablePanelGroup
          id="resize-panel-group-1"
          direction="horizontal"
          class="h-full items-stretch"
        >
          <ResizablePanel
            id="resize-panel-1"
            :default-size="defaultLayout[0]"
            :collapsed-size="navCollapsedSize"
            collapsible
            :class="cn(isCollapsed && 'transition-all duration-100 ease-in-out')"
            style="width: 270px !important; flex-basis: 270px !important;"
            @expand="onExpand"
            @collapse="onCollapse"
          >
            <UserDropdown />
            <ScrollArea type="auto" class="h-full overflow-visible px-4">
              <EncryptedFolderTree></EncryptedFolderTree>
              <router-link to="/">Home</router-link>
              <router-link to="/encrypted">Encrypted</router-link>
              <router-link to="/shared">Shared</router-link>
              <Button @click="openDialog('shareFolder')" variant="outline" class="w-full">
                <Icon name="lucide:share" class="mr-2 h-4 w-4" />
                {{ i18n.t('share.title') }}
              </Button>
              <hr>
              <FolderTree v-if="currentFolderId" :currentFolderId="currentFolderId"></FolderTree>
            </ScrollArea>
          </ResizablePanel>
          <ResizableHandle id="resize-handle-1" with-handle />
          <ResizablePanel id="resize-panel-2" :default-size="defaultLayout[1]" :min-size="50">
            <ScrollArea type="auto" class="h-full mb-8">
              <router-view></router-view>
            </ScrollArea>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>

    <NeedToLoginDialog
      :open="dialogs.needToLogin.isOpen"
      @openChange="(value: boolean) => value ? openDialog('needToLogin') : closeDialog('needToLogin')"
    />

    <NeedUpgradeDialog
      :open="dialogs.needUpgrade.isOpen"
      @openChange="(value: boolean) => value ? openDialog('needUpgrade') : closeDialog('needUpgrade')"
    />

    <SetEncryptKeyDialog
      :open="dialogs.setEncryptKey.isOpen" 
      @openChange="(value: boolean) => value ? openDialog('setEncryptKey') : closeDialog('setEncryptKey')"
    />

    <ResetEncryptKeyDialog 
      :open="dialogs.resetEncryptKey.isOpen" 
      @openChange="(value: boolean) => value ? openDialog('resetEncryptKey') : closeDialog('resetEncryptKey')"
    />

    <UnlockEncryptDialog 
      :open="dialogs.unlockEncrypt.isOpen" 
      @openChange="(value: boolean) => value ? openDialog('unlockEncrypt') : closeDialog('unlockEncrypt')"
    />

    <NewEncryptedFolderDialog
      :open="dialogs.newEncryptedFolder.isOpen"
      @openChange="(value: boolean) => value ? openDialog('newEncryptedFolder') : closeDialog('newEncryptedFolder')"
    />

    <ShareFolderDialog
      :open="dialogs.shareFolder.isOpen"
      :default-folder-id="dialogs.shareFolder.props?.defaultFolderId"
      @openChange="(value: boolean) => value ? openDialog('shareFolder') : closeDialog('shareFolder')"
      @share-created="(data) => console.log('Share created:', data)"
    />
  </TooltipProvider>
</template>
