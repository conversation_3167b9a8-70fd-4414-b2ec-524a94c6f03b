<script setup lang="ts">
import type { RxDatabase } from 'rxdb'
import { useRout<PERSON>, useRouter } from 'vue-router';
import { format as dateFormat } from 'date-fns'
import ViewOptions from './components/ViewOptions.vue'
import Show from './components/Show.vue'
import FolderEditDialog from './components/FolderEditDialog.vue'
import BookmarkEditDialog from './components/BookmarkEditDialog.vue'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { refDebounced } from '@vueuse/core'
import Icon from '@/components/Icon.vue'
import { onClickOutside } from '@vueuse/core'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from '@/components/ui/context-menu'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import ListItem from './ListItem.vue'
import Breadcrumbs from './components/Breadcrumbs.vue'
import { Badge } from '@/components/ui/badge'
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import TagEditDialog from './components/TagEditDialog.vue'
import BulkAddTagDialog from './components/BulkAddTagDialog.vue'
import BulkRemoveTagDialog from './components/BulkRemoveTagDialog.vue'
import BulkNoteDialog from './components/BulkNoteDialog.vue'
import BulkEmojiDialog from './components/BulkEmojiDialog.vue'
import SortingOptions from './components/SortingOptions.vue'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'


const route = useRoute();
const router = useRouter();

// 首先声明所有响应式变量
const selectedTags = ref<string[]>([]);
const totalBookmarksCount = ref<number>(0);
const search = ref<string>('')
const searchDebounced = refDebounced(search, 35)
const includeSubfolders = ref(true)

// const db = inject<RxDatabase>('db')!

const props = defineProps<{
  currentFolderId: string
}>()

// 注入 setSelectedFolderId 方法
const setSelectedFolderId = inject<(folderId: string) => void>('setSelectedFolderId')

const flatBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])

// select
import { select, selectAll } from '@/utils/multiselect'
const pivotId = ref<string | null>(null)
const selectedIds = ref<string[]>([])
const ids = ref<string[]>([])

const selectedItem = computed(() => {
  return flatBookmarks.value.find(item => item.id === selectedIds.value[0])
})
const editBookmarkDialogOpen = ref(false)
const editFolderDialogOpen = ref(false)
const editTagDialogOpen = ref(false)
const currentTag = ref<{ id: string; name: string } | null>(null)
const bulkAddTagDialogOpen = ref(false)
const bulkRemoveTagDialogOpen = ref(false)
const bulkNoteDialogOpen = ref(false)
const bulkEmojiDialogOpen = ref(false)

function handleClick(itemId: string, e?: PointerEvent) {
  if (e?.ctrlKey || e?.metaKey) {
    const [pivot, newIds] = select<string>(
      selectedIds.value,
      pivotId.value!,
      itemId
    )
    pivotId.value = pivot
    selectedIds.value = newIds
  } else if (e?.shiftKey) {
    const selected = selectAll<string>({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      ids: ids.value!,
      id: itemId
    })
    selectedIds.value = selected
  } else {
    if (selectedIds.value.includes(itemId) && selectedIds.value.length === 1) {
      pivotId.value = null
      selectedIds.value = []
    } else {
      pivotId.value = itemId
      selectedIds.value = [itemId]
    }
  }
}

function openBookmark(item: chrome.bookmarks.BookmarkTreeNode) {
  // 在新标签页中打开书签
  if (item.url) {
    browser.tabs.create({ url: item.url })
    selectedIds.value = [item.id]
  } else {
    setSelectedFolderId?.(item.id)
  }
}

// 实现类似 chrome 书签管理器的批量打开书签功能，循环打开 selectedIds 中的书签
// type 分别为普通打开，在新窗口中打开，以及在无痕式窗口中打开
function bulkOpenBookmark(type: 'tab' | 'window' | 'incognito') {
  const urls = selectedIds.value
    .map(id => flatBookmarks.value.find(item => item.id === id)?.url)
    .filter(Boolean) as string[];
  if (urls.length === 0) return;
  if (type === 'tab') {
    urls.forEach(url => browser.tabs.create({ url }));
  } else {
    // 在新窗口/无痕窗口一次性打开所有标签页
    browser.windows.create({
      url: urls,
      incognito: type === 'incognito',
      state: 'maximized'
    });
  }
}

// 添加专门的右键处理函数
function handleRightClick(e: MouseEvent, itemId: string) {  
  // 如果当前右键项不在选中列表中
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
    pivotId.value = itemId
  }
}

function openEditTagDialog(tag: { id: string; name: string }) {
  currentTag.value = tag
  editTagDialogOpen.value = true
}

function handleEdit(id?: string) {
  if (id) {
    selectedIds.value = [id]
  }
  if (selectedItem?.value?.url) {
    editBookmarkDialogOpen.value = true
  } else {
    editFolderDialogOpen.value = true
  }
}

function isSelected(id: string) {
  return selectedIds.value.includes(id)
}

const multiSelectTarget = ref<HTMLElement | null>(null)
const actionBarRef = ref<HTMLElement | null>(null)
const contextMenuOpen = ref(false);
const showActionBar = computed(() => selectedIds.value.length > 1)
const isShowActionBarMenuOpen = ref(false)
onClickOutside(multiSelectTarget, () => {
  if (
    !contextMenuOpen.value && 
    !editFolderDialogOpen.value && 
    !editBookmarkDialogOpen.value && 
    !confirmDeleteDialog.value &&
    !bulkAddTagDialogOpen.value &&
    !bulkRemoveTagDialogOpen.value &&
    !bulkNoteDialogOpen.value &&
    !bulkEmojiDialogOpen.value &&
    !isShowActionBarMenuOpen.value
  ) {
    selectedIds.value = []
    pivotId.value = null
  }
}, {ignore: [actionBarRef]});

function itemDragStart(itemId: string) {
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
  }
}

// 递归计算子节点的所有文件夹数和 bookmarks 数
const subFolderCount = ref(0)
const subBookmarkCount = ref(0)
async function countBookmarksAndFoldersById(currentFolderId: string) {
  const start = performance.now();
  let folderCount = 0;
  let bookmarkCount = 0;

  const bookmarks = await browser.bookmarks.getSubTree(currentFolderId)
  function traverse(node: chrome.bookmarks.BookmarkTreeNode) {
    // 如果当前节点有 url 属性，那么它是一个书签
    if (node.url) {
      bookmarkCount++;
    } else if (node.children && node.children.length > 0) {
      // 如果当前节点有 children 属性且不为空，那么它是一个文件夹
      folderCount++;
      node.children.forEach(traverse); // 递归遍历子节点
    }
  }

  function findAndCount(node: chrome.bookmarks.BookmarkTreeNode) {
    if (node.id === currentFolderId) {
      traverse(node); // 找到目标节点，开始遍历其子节点
    } else if (node.children) {
      // 遍历当前节点的子节点
      node.children.forEach(findAndCount);
    }
  }

  // 从根节点开始查找目标节点及其子树
  bookmarks.forEach(findAndCount);
  const end = performance.now();
  subFolderCount.value = folderCount
  subBookmarkCount.value = bookmarkCount
  console.log(`folderCount took ${(end - start).toFixed(0)} milliseconds`)
  return { folderCount, bookmarkCount };
}

// 通用的分批渲染函数
const animationFrameId = ref<number | null>(null);
async function renderInBatches(items: chrome.bookmarks.BookmarkTreeNode[]) {
  const start = performance.now();
  // 取消之前的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }

  let sorted = [...items];
  
  // 仅在非默认排序时进行排序
  if (sortBy.value.field) {
    sorted = sorted.sort((a, b) => {
      let compareResult = 0;
      
      // 文件夹优先排序
      // const aIsFolder = !a.url;
      // const bIsFolder = !b.url;
      // if (aIsFolder !== bIsFolder) return aIsFolder ? -1 : 1;

      // 根据选定字段排序
      switch (sortBy.value.field) {
        case 'default':
          // 将ID转换为数字比较
          compareResult = (a.index || 0) - (b.index || 0);
          break;
        case 'id':
          // 将ID转换为数字比较
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
          break;
        case 'title':
          compareResult = (a.title || '').localeCompare(b.title || '');
          // 添加标题相同时的次级排序
          if (compareResult === 0) {
            const numA = parseInt(a.id, 10);
            const numB = parseInt(b.id, 10);
            compareResult = (numA - numB) || a.id.localeCompare(b.id);
          }
          break;
        case 'dateAdded':
          compareResult = (a.dateAdded || 0) - (b.dateAdded || 0);
          if (compareResult === 0) {
            const numA = parseInt(a.id, 10);
            const numB = parseInt(b.id, 10);
            compareResult = (numA - numB) || a.id.localeCompare(b.id);
          }
          break;
        case 'dateLastUsed':
          compareResult = ((a as any)?.dateLastUsed || 0) - ((b as any)?.dateLastUsed || 0);
          if (compareResult === 0) {
            const numA = parseInt(a.id, 10);
            const numB = parseInt(b.id, 10);
            compareResult = (numA - numB) || a.id.localeCompare(b.id);
          }
          break;
        case 'url':
          const normalizeUrl = (url?: string) => {
            if (!url) return '';
            return url.replace(/^https?:\/\//i, '').toLowerCase();
          };
          compareResult = normalizeUrl(a.url).localeCompare(normalizeUrl(b.url));
          // 添加URL相同时的次级排序
          if (compareResult === 0) {
            const numA = parseInt(a.id, 10);
            const numB = parseInt(b.id, 10);
            compareResult = (numA - numB) || a.id.localeCompare(b.id);
          }
          break;
      }
      
      return sortBy.value.direction === 'asc' ? compareResult : -compareResult;
    });
  }
  
  ids.value = sorted.map(x => x.id);
  let index = 0;
  const batchSize = 15;
  let currentBatch: chrome.bookmarks.BookmarkTreeNode[] = [];
  // 清空当前列表 - 会导致闪烁
  flatBookmarks.value = [];

  function processBatch() {
    const end = Math.min(index + batchSize, sorted.length);
    for (; index < end; index++) {
      currentBatch.push(sorted[index]);
    }
    
    flatBookmarks.value = [...currentBatch];
    
    if (index < sorted.length) {
      animationFrameId.value = requestAnimationFrame(processBatch);
    } else {
      const end = performance.now();
      console.log(`All items processed took ${(end - start).toFixed(0)} milliseconds`);
      animationFrameId.value = null;
    }
  }

  processBatch();
}

// 在props定义后添加当前文件夹的ref
const currentFolder = ref<chrome.bookmarks.BookmarkTreeNode>();
// 在现有响应式变量后添加排序相关状态
const sortBy = ref<{ field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }>({
  field: 'default',
  direction: 'asc'
});

// 修改setBookmarks函数，添加排序逻辑
async function setBookmarks() {
  const start = performance.now();
  
  const bookmarks = await browser.bookmarks.getChildren(props.currentFolderId);
  
  renderInBatches(bookmarks);
  countBookmarksAndFoldersById(props.currentFolderId);
  const end = performance.now();
  console.log(`数据加载耗时 ${(end - start).toFixed(0)} 毫秒`);
}

// 修改排序方法参数类型
function handleSort(field: 'default' | 'title' | 'dateAdded' | 'url' | 'id') {
  if (sortBy.value.field === field) {
    // 切换排序方向
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc';
  } else {
    // 切换排序字段并重置方向
    sortBy.value.field = field;
    sortBy.value.direction = 'asc';
  }
  renderInBatches(flatBookmarks.value); // 重新加载数据应用排序
}

// 递归获取所有书签ID（包含子文件夹）
async function getAllBookmarkIds(folderId: string): Promise<string[]> {
  const nodes = await browser.bookmarks.getSubTree(folderId);
  console.warn('--------- nodes -----------', nodes)
  const bookmarkIds: string[] = [];

  function traverse(node: chrome.bookmarks.BookmarkTreeNode) {
    if (node.url) { // 书签节点
      bookmarkIds.push(node.id);
    } else if (node.children) { // 文件夹节点
      node.children.forEach(traverse);
    }
  }

  nodes.forEach(traverse);
  return bookmarkIds;
}

const extras = ref<any[]>([])
// 获取去重后的标签列表
const uniqueTags = ref<Array<{ id: string, name: string, bookmarkIds: string[] }>>([]);
async function getUniqueTagsAndExtras() {
  const start = performance.now();
  let flattenedSubBookmarks: chrome.bookmarks.BookmarkTreeNode[] = []
  if (includeSubfolders.value) {
    flattenedSubBookmarks = await getFlattenedSubTree(props.currentFolderId)
  } else {
    flattenedSubBookmarks = await browser.bookmarks.getChildren(props.currentFolderId)
  }
  // 获取所有 flattenedSubBookmarks 的 bookmarkIds，排除那些没有 url 的节点（文件夹）
  const bookmarkIds = flattenedSubBookmarks
    // .filter(item => item.url)
    .map(item => item.id)
  // 2. 查询所有包含这些书签ID的标签
  const tags = await db!.tags.find({
    selector: {
      bookmarkIds: {
        $elemMatch: { $in: bookmarkIds }
      }
    }
  }).exec();
  extras.value = await db!.extras.find({
    selector: {
      bookmarkId: { $in: bookmarkIds }
    }
  }).exec();
  console.warn('--------- tags -----------', tags)
  uniqueTags.value = tags
  const end = performance.now();
  console.warn(`getUniqueTagsAndExtras took ${(end - start).toFixed(0)} milliseconds`)
}

// 在 watch 监听器中添加取消逻辑
watch(() => props.currentFolderId, async (newVal) => {  
  // 取消正在进行的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }
  
  // 保持原有重置逻辑...
  ids.value = [];
  // ...其他重置代码不变
  // selectedTags.value = [];
  selectedTags.value.splice(0);
  search.value = '';
  selectedIds.value = [];
  pivotId.value = null;
  
  // 顺序执行关键操作
  setBookmarks();
  getUniqueTagsAndExtras();
}, { immediate: true });

watch([searchDebounced, selectedTags], async () => {
  await searchItems()
}, { immediate: false});

watch(() => includeSubfolders.value, async () => {
  selectedTags.value.splice(0)
  await getUniqueTagsAndExtras()
  await searchItems()
}, { immediate: false});

// 搜索
const isFiltered = computed(() => search.value.length > 0 || selectedTags.value.length > 0)
async function searchItems() {
  console.warn('--------- 触发搜索 -----------')
  const allstart = performance.now();
  const folderbookmarks = await browser.bookmarks.getChildren(props.currentFolderId);
  
  if (!searchDebounced.value && selectedTags.value.length === 0) {
    renderInBatches(folderbookmarks);
    console.warn('--------- 没有搜索词也没有选中 Tag -----------')
    return
  }

  // 获取执行时间
  let filtered = includeSubfolders.value
    ? await getFlattenedSubTree(props.currentFolderId)
    : folderbookmarks;
  
  // 先应用标签筛选（即使结果为空）
  if (selectedTags.value.length > 0) {
    const start2 = performance.now();
    // 获取所有选中标签的文档
    const results = await Promise.all(
      selectedTags.value.map(tagName => 
        db!.tags.findOne({ selector: { name: tagName } }).exec()
      )
    );
    // 收集所有标签的bookmarkIds
    const allIds = results
      .filter(Boolean)
      .map(doc => doc.bookmarkIds || []);
    // 计算交集
    const intersection = allIds.reduce((a, b) => 
      a.filter((id: string) => b.includes(id))
    );
    filtered = filtered.filter(item => intersection.includes(item.id));
    const end2 = performance.now();
    console.warn(`search tags took ${(end2 - start2).toFixed(0)} milliseconds`, filtered)
  }
  
  // 关键词搜索
  if (searchDebounced.value) {
    const start3 = performance.now();
    // 搜索 title 和 url ，并返回匹配关键词的 ids
    const lowerTerm = searchDebounced.value.toLowerCase();
    // 搜索 db.bookmarks 表中的 title 和 url 字段, 并返回匹配的 ids 合集
    const keywordFilteredIds = filtered
      .filter(item => {
        const title = item.title?.toLowerCase() || '';
        const url = item.url?.toLowerCase() || '';
        return title.includes(lowerTerm) || url.includes(lowerTerm);
      })
      .map(item => item.id);
    // 搜索 db.extra 表中的 note 和 emoji 字段, 并返回匹配的 extra 表数据
    const extraResults = await db!.extras.find({
      selector: {
        $or: [
          { note: { $regex: searchDebounced.value } },
          { emoji: { $regex: searchDebounced.value } }
        ]
      }
    }).exec();
    // 提取匹配的 extra 表数据中的 bookmarkId 字段合并为 ids 合集并去重
    const extraBookmarkIds = Array.from(new Set(
      extraResults
        .filter(doc => doc.bookmarkId) // 过滤掉空值
        .map(doc => doc.bookmarkId)
    ));
    // 合并 keywordFilteredIds 和 extraBookmarkIds 并去重
    const combinedIds = [...new Set([...keywordFilteredIds, ...extraBookmarkIds])];
    filtered = filtered.filter(item => combinedIds.includes(item.id));
    const end3 = performance.now();
    console.log(`search keyword took ${(end3 - start3).toFixed(0)} milliseconds`)
  }
  
  totalBookmarksCount.value = filtered.length
  // 分批渲染，最多渲染 100 个结果
  renderInBatches(filtered.slice(0, 100));
  const allend = performance.now();
  console.log(`搜索结果 took ${(allend - allstart).toFixed(0)} milliseconds`)
}

// 新增递归获取平铺书签的函数
async function getFlattenedSubTree(folderId: string) {
  const start = performance.now();
  const tree = await browser.bookmarks.getSubTree(folderId)
  
  const flatten = (nodes: chrome.bookmarks.BookmarkTreeNode[]): chrome.bookmarks.BookmarkTreeNode[] => {
    return nodes.flatMap(node => {
      const children = node.children ? flatten(node.children) : []
      return [node, ...children]
    })
  }
  const end = performance.now();
  console.log(`getFlattenedSubTree took ${(end - start).toFixed(0)} milliseconds`, tree)

  return flatten(tree)
}

function resetSearch() {
  search.value = ''
  selectedTags.value = []
}

// 添加标签操作函数
function toggleTag(tagName: string) {
  const index = selectedTags.value.indexOf(tagName);
  if (index === -1) {
    selectedTags.value = [...selectedTags.value, tagName];
  } else {
    selectedTags.value = selectedTags.value.filter(t => t !== tagName);
  }
}

// 删除处理函数
const confirmDeleteDialog = ref(false)
const performDelete = async () => {
  try {
    // 获取所有选中项目的详细信息
    const items = await Promise.all(
      selectedIds.value.map(id => 
        browser.bookmarks.get(id).then(results => results[0])
      )
    );

    // 根据类型执行不同的删除操作
    await Promise.all(
      items.map(item => {
        if (item.url) { // 书签节点
          return browser.bookmarks.remove(item.id);
        } else { // 文件夹节点
          return browser.bookmarks.removeTree(item.id);
        }
      })
    );

    selectedIds.value = [];
    confirmDeleteDialog.value = false;
  } catch (error) {
    console.error('删除失败:', error);
  }
}

function handleRuntimeMessage(request: { type: string }) {
  console.warn('--------- manage list 收到消息 -----------!', request)
  if (request.type === 'bookmark-change') {
    setBookmarks()
  }
}

onMounted(async () => {
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
  router.isReady().then(() => {
    if (route.query.bookmarkId) {
      selectedIds.value = [route.query.bookmarkId as string]
    }
  });
});

onUnmounted(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
  }
});
</script>

<template>
  <div>
    <div class="flex justify-between mt-4 items-center">
      <div class="flex flex-col space-y-1 items-start">
        <Breadcrumbs :folderId="currentFolderId"></Breadcrumbs>
        <Tooltip>
          <TooltipTrigger as-child>
            <h2 class="text-2xl font-bold">{{ currentFolder?.title }}</h2>
          </TooltipTrigger>
          <TooltipContent>
            <p class="text-sm">
              <span class="font-bold">{{ subFolderCount }}</span> sub folders, 
              <span class="font-bold">{{ subBookmarkCount }}</span> bookmarks, 
              <span class="font-bold">{{ uniqueTags.length }}</span> tags.
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
    
    <div class="flex items-center justify-between mt-4">
      <div class="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter bookmarks ..."
          v-model="search"
          class="h-10 w-[220px] lg:w-[350px] rounded-full pl-4 bg-secondary pt-0 focus-visible:ring-2"
        />
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="includeSubfolders" 
            :model-value="includeSubfolders"
            class="h-4 w-4"
          />
          <label 
            for="includeSubfolders" 
            class="text-sm font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Sub folders
          </label>
        </div>
        <Button
          v-if="isFiltered"
          variant="ghost"
          class="h-8 px-2 lg:px-3"
          @click="resetSearch()"
        >
          Reset
          <Icon name="radix-icons:cross-2" />
        </Button>
      </div>
      <div class="flex items-center gap-2">
        <SortingOptions 
          :sort-by="sortBy"
          @sort="handleSort"
        />
        <ViewOptions />
      </div>
    </div>

    <div v-if="uniqueTags.length === 0" class="flex gap-2 flex-wrap mt-4 text-muted-foreground items-center">
      <Button 
          variant="secondary"
          size="xs"
          disabled
          :class="cn(
            'flex flex-col items-start gap-2 border p-3 text-left text-sm',
          )"
          @click="selectedTags = []"
        >
          # All
        </Button> No tags
    </div>
    <ul v-else class="flex gap-2 flex-wrap mt-4">
      <li>
        <Button 
          :variant="selectedTags.length === 0 ? 'default' : 'secondary'"
          size="xs"
          :class="cn(
            'flex flex-col items-start gap-2 border p-3 text-left text-sm',
          )"
          @click="selectedTags = []"
        >
          # All
        </Button>
      </li>
      <li v-for="tag in uniqueTags" class="items-center flex gap-1">
        <HoverCard :openDelay="100" :closeDelay="0" :delayDuration="0">
          <HoverCardTrigger as-child>
          <Button 
            :variant="selectedTags.includes(tag.name) ? 'default' : 'outline'"
            size="xs"
            @click="toggleTag(tag.name)"
          >
            {{ tag.name }}
          </Button>
          </HoverCardTrigger>
          <HoverCardContent side="bottom" class="max-w-40 p-1">
            <div class="flex items-center gap-2 hover:bg-muted p-2 rounded-md"  @click="openEditTagDialog(tag)">
              <Icon name="lucide:edit-3" />
              <span>Edit</span>
            </div>
          </HoverCardContent>
        </HoverCard>
      </li>
    </ul>

    <div class="mt-4">
      <h3 
        v-if="isFiltered" 
        class="text-sm text-muted-foreground"
      >
        {{ i18n.t('options.list.searchResults', [totalBookmarksCount, flatBookmarks.length, 100]) }}
      </h3>
    </div>
    <div v-if="flatBookmarks.length" class="rounded-md border mt-4 py-2" ref="multiSelectTarget">
      <ContextMenu 
          v-model:open="contextMenuOpen"
          @open-change="(open: boolean) => contextMenuOpen = open"
        >
          <ContextMenuTrigger>
          <ul>
            <ListItem
              v-for="item in flatBookmarks"
              :key="item.id"
              :data-state="isSelected(item.id) && 'selected'"
              @contextmenu="(e: MouseEvent) => handleRightClick(e, item.id)"
              @click.stop="(e: PointerEvent) => handleClick(item.id, e)"
              @dblclick="openBookmark(item)"
              class="transition-none data-[state=selected]:bg-muted"
              :item="item"
              :selectedIds="selectedIds"
              :tags="uniqueTags.filter(tag => tag.bookmarkIds.includes(item.id))"
              :extra="extras.find(extra => extra.bookmarkId === item.id)"
              @drag-start="itemDragStart"
              @open-context-menu="contextMenuOpen = true"
              @delete="(id) => { handleClick(id); confirmDeleteDialog = true }"
              @edit="(id) => handleEdit(id)"
            >
            </ListItem>
          </ul>
          </ContextMenuTrigger>
          <ContextMenuContent 
            class="v-context-menu-content"
          >
            <ContextMenuItem 
              :disabled="selectedIds.length > 1"
              @select="handleEdit()"
            >
              {{ i18n.t('context_menu.edit') }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="confirmDeleteDialog = true"
            >
              {{ i18n.t('context_menu.deleteWithCount', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuSub>
              <ContextMenuSubTrigger>
                {{ i18n.t('context_menu.bulkActions') }}
              </ContextMenuSubTrigger>
              <ContextMenuSubContent>
                <ContextMenuItem 
                  @select="bulkAddTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkAddTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkRemoveTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkRemoveTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkNoteDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkNote') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkEmojiDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkEmoji') }}
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
            <ContextMenuSeparator />
            <ContextMenuItem 
              @select="bulkOpenBookmark('tab')"
            >
              {{ i18n.t('context_menu.openAll', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('window')"
            >
              {{ i18n.t('context_menu.openInNewWindow', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('incognito')"
            >
              {{ i18n.t('context_menu.openIncognito', [selectedIds.length]) }}
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
    </div>
    <div v-else class="flex flex-col h-48 text-center rounded-md border mt-4 justify-center gap-2">
      <h3 class="text-lg font-semibold">
        {{ i18n.t('options.list.noResults') }}
      </h3>
      <p class="text-sm text-muted-foreground">
        {{ i18n.t('options.list.noResultsDescription') }}
      </p>
    </div>

    <FolderEditDialog
      :open="editFolderDialogOpen"
      :item="selectedItem"
      @open-change="(open) => (editFolderDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <BookmarkEditDialog
      :open="editBookmarkDialogOpen"
      :item="selectedItem"
      @open-change="(open) => (editBookmarkDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <BulkAddTagDialog
      :open="bulkAddTagDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open) => (bulkAddTagDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <BulkRemoveTagDialog
      :open="bulkRemoveTagDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open) => (bulkRemoveTagDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <BulkNoteDialog
      :open="bulkNoteDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open) => (bulkNoteDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <BulkEmojiDialog
      :open="bulkEmojiDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open) => (bulkEmojiDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <TagEditDialog
      :open="editTagDialogOpen"
      :item="currentTag"
      @open-change="(open) => (editTagDialogOpen = open)"
      @saved="getUniqueTagsAndExtras"
    />

    <AlertDialog v-model:open="confirmDeleteDialog">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Deletion of {{ selectedIds.length }} Items</AlertDialogTitle>
          <AlertDialogDescription>
            This action will permanently delete the selected bookmarks/folders ({{ selectedIds.length }} items). This operation cannot be undone!
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant="outline" @click="confirmDeleteDialog = false">
            Cancel
          </Button>
          <Button variant="destructive" @click="performDelete()">
            Confirm Delete
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <div 
      v-show="showActionBar"
      class="fixed top-14 left-0 right-0 bg-secondary border-b z-50 mx-auto shadow-sm"
    >
      <div class="container flex items-center h-14 gap-4 justify-between max-w-5xl mx-auto">
        <div class="flex items-center gap-2">
          <Icon name="radix-icons:cross-2" @click="selectedIds = []" class="size-4 mr-2" />
          <span class="text-sm text-muted-foreground">
            {{ selectedIds.length }} selected
          </span>
        </div>
        
        <div ref="actionBarRef" class="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            @click="confirmDeleteDialog = true"
          >
            <Icon name="lucide:trash-2" class="size-4" />
          </Button>
          <Icon name="radix-icons:divider-vertical" class="size-4 text-muted-foreground/75" />
          <Button
            variant="outline"
            size="icon"
            @click="bulkEmojiDialogOpen = true"
          >
            <Icon name="lucide:smile" class="size-4" />
          </Button>
          <DropdownMenu v-model:open="isShowActionBarMenuOpen">
            <DropdownMenuTrigger as-child>
              <Button
                variant="ghost"
                size="icon"
              >
                <Icon name="lucide:ellipsis" class="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              class="v-context-menu-content"
            >
              <DropdownMenuItem 
                :disabled="selectedIds.length > 1"
                @select="handleEdit()"
              >
                {{ i18n.t('context_menu.edit') }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="confirmDeleteDialog = true"
              >
                {{ i18n.t('context_menu.deleteWithCount', [selectedIds.length]) }}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                @select="bulkAddTagDialogOpen = true"
              >
                {{ i18n.t('context_menu.bulkAddTag') }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="bulkRemoveTagDialogOpen = true"
              >
                {{ i18n.t('context_menu.bulkRemoveTag') }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="bulkNoteDialogOpen = true"
              >
                {{ i18n.t('context_menu.bulkNote') }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="bulkEmojiDialogOpen = true"
              >
                {{ i18n.t('context_menu.bulkEmoji') }}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                @select="bulkOpenBookmark('tab')"
              >
                {{ i18n.t('context_menu.openAll', [selectedIds.length]) }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="bulkOpenBookmark('window')"
              >
                {{ i18n.t('context_menu.openInNewWindow', [selectedIds.length]) }}
              </DropdownMenuItem>
              <DropdownMenuItem 
                @select="bulkOpenBookmark('incognito')"
              >
                {{ i18n.t('context_menu.openIncognito', [selectedIds.length]) }}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> 
        </div>
      </div>
    </div>
  </div>
</template>
