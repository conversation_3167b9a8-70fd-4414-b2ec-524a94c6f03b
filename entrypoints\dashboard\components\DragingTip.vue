<script setup lang="ts">
defineProps<{
  item: chrome.bookmarks.BookmarkTreeNode
  selectedIds: string[]
}>()
</script>

<template>
  <div class="w-36 border-4 border-blue-500 bg-blue-400 text-white rounded-full text-sm font-medium px-3 py-1.5 truncate">
    <span>{{ item.title }}</span> 
    <div
      v-if="selectedIds.length > 1" 
      class="flex absolute h-6 min-w-6 -top-2 -right-2 bg-red-500 text-center items-center justify-center rounded-full">
      {{ selectedIds.length }}
    </div>
  </div>
</template>
