<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem, DropdownMenuGroup } from  '@/components/ui/dropdown-menu'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { useColorMode } from '@vueuse/core'
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { runRxDBReplication, cancelRxDBReplication } from '@/rxdb/sync';

import Icon from '@/components/Icon.vue';

const { clear: clearUserSession, refreshSession, currentUser } = useUserSession()
const { currentProfileUser } = useProfileUser()

const colorMode = useColorMode();
const toggleColorMode = () => {
  const newColorMode = colorMode.value === 'light' ? 'dark' : 'light'; // 切换模式
  browser.storage.sync.set({ colorMode: newColorMode }); // 持久化同步存储
};

const website = import.meta.env.VITE_WEBSITE

async function test() { 
  // analytics.setEnabled(true);
  // await analytics.page();
  // analytics.autoTrack(document.body);
  // analytics.event('manual_test', { test: true })
}

const testFetch = async () => {
  // const res = await fetch(`${WEBSITE_URL}/api/bookmarks`)
  refreshSession()
  console.log(currentUser.value)
}

const testSync = async () => {
  await cancelRxDBReplication()
  await runRxDBReplication()
}
</script>
<template>
  <header
    class="border-b h-14 flex items-center box-border"
    :class="cn('sticky top-0 z-40')"
  >
    <div class="mx-auto w-full px-4">
      <!-- mobile nav -->
      <div class="z-20 flex w-full flex-col items-center md:hidden">
        <div class="flex w-full items-center">
          <div class="flex-auto">
            <router-link to="/" class="text-xl font-semibold tracking-tight">Gomark</router-link>
          </div>
        </div>
      </div>

      <div class="mx-auto hidden items-center justify-between md:flex">
        <div class="flex-1">
          <router-link to="/" class="text-xl font-semibold tracking-tight">Gomark</router-link>
        </div>
        <!-- <Button @click="test">
          test
        </Button> -->
        <!-- nav -->
        <!-- <ul class="flex items-center">
          <li>
            <a
              class="rounded-md px-2 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/"
            >
              nav
            </a>
          </li>
        </ul> -->
        <div class="flex flex-1 justify-end gap-2 items-center">
          <Button @click="toggleColorMode" variant="ghost" size="icon">
            <Icon :name="colorMode === 'light' ? 'lucide:moon' : 'lucide:sun'" />
          </Button>
          <Icon name="radix-icons:divider-vertical" class="text-muted-foreground/55" />
          <Tooltip>
            <TooltipTrigger as-child>
              <Button variant="ghost" size="icon" as-child>
                <a href="mailto:<EMAIL>" target="_blank">
                  <Icon name="lucide:mail" />
                </a>
              </Button>
            </TooltipTrigger>
            <TooltipContent class="leading-6 bg-background text-foreground border shadow-md">
              <p>Feedback & Bugs</p>
              <p class="font-semibold"><EMAIL></p>
            </TooltipContent>
          </Tooltip>
          <Button variant="ghost" size="icon" as-child>
            <a href="https://chrome.google.com/webstore/detail/hkhpfcdoekegjilpmfmemgllocnpnbbd/reviews" target="_blank">
              👏
            </a>
          </Button>
          <div v-if="currentUser">
            <br>
            {{ currentProfileUser }}
            <DropdownMenu>
              <DropdownMenuTrigger class="text-muted-foreground hover:text-foreground">
                {{ currentUser.email }} <Icon name="lucide:chevrons-up-down" />
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="w-56"
                align="end"
              >
                <DropdownMenuLabel class="flex font-normal">
                  <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{ currentUser.name }}</span>
                    <span class="truncate text-xs text-muted-foreground">{{ currentUser.email }}</span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem @click="clearUserSession">
                  <Icon
                    name="lucide:log-out"
                    class="mr-2"
                  />
                  {{ i18n.t('options.header.logout') }}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div v-else>
            <Button
              as-child
              variant="ghost"
              class="rounded-full"
              size="lg"
            >
              <a
                class="font-semibold text-muted-foreground hover:text-foreground"
                :href="`${website}/auth/login-with-password`"
                target="_blank"
              >
                {{ i18n.t('options.header.signIn') }}
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>
