<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const props = defineProps<{
  open: boolean
  item: { id: string; name: string } | null
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    name: z.string().min(1),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  if (!props.item) return
  
  try {
    // 更新标签时使用 _id 作为主键
    rxdb.tags.findOne({
      selector: {
        id: props.item!.id,
      }
    }).patch({
      name: values.name,
      updatedAt: new Date().toISOString(),
    });
    emit('saved', { ...props.item, name: values.name })
    emit('openChange', false)
  } catch (error) {
    console.error('更新失败:', error)
  }
})

const handleDelete = async () => {
  if (!props.item) return
  
  try {
    const confirm = window.confirm(i18n.t('dialog.confirm_delete'))
    if (!confirm) return

    rxdb.tags.findOne({
      selector: {
        id: props.item!.id,
      }
    }).remove();
    emit('saved', { ...props.item, deleted: true })
    emit('openChange', false)
    toast.success(i18n.t('dialog.delete_success'))
  } catch (error) {
    toast.error(i18n.t('dialog.delete_failed'))
  }
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ i18n.t('form.tags.edit') }}</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>

      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="name"
          :value="item?.name" 
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="name"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <div class="flex mt-2 gap-2 w-full justify-between">
          <Button
            type="button"
            variant="destructive"
            @click="handleDelete"
        >
          {{ i18n.t('dialog.action.delete') }}
        </Button>
        <Button
          type="submit"
          form="editForm"
        >
            {{ i18n.t('dialog.action.save') }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
