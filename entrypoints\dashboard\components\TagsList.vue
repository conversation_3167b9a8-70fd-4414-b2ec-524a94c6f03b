<script setup lang="ts">
import { Button } from '@/components/ui/button';
import Icon from '@/components/Icon.vue';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { getFlattenedSubTree } from '@/utils/bookmark';
import TagEditDialog from './TagEditDialog.vue';

const props = defineProps<{
  uniqueTags: any[],
  selectedTags: string[]
}>();

const emit = defineEmits<{
  'update:selectedTags': [tags: string[]],
  'tags:edited': []
}>();

const currentTag = ref<{ id: string; name: string } | null>(null);
const editTagDialogOpen = ref(false);

// 添加标签操作函数
function toggleTag(tagName: string) {
  const newSelectedTags = [...props.selectedTags];
  const index = newSelectedTags.indexOf(tagName);
  
  if (index === -1) {
    newSelectedTags.push(tagName);
  } else {
    newSelectedTags.splice(index, 1);
  }
  
  emit('update:selectedTags', newSelectedTags);
}

function openEditTagDialog(tag: { id: string; name: string }) {
  currentTag.value = tag;
  editTagDialogOpen.value = true;
}

function clearTags() {
  emit('update:selectedTags', []);
}
</script>

<template>
  <div>
    <div v-if="uniqueTags.length === 0" class="flex gap-2 flex-wrap mt-4 text-muted-foreground items-center">
      <Button 
        variant="secondary"
        disabled
        :class="cn(
          'flex flex-col items-start gap-2 border p-3 text-left text-sm',
        )"
        @click="clearTags"
      >
        # All
      </Button> No tags
    </div>
    <ul v-else class="flex gap-2 flex-wrap mt-4">
      <li>
        <Button 
          :variant="selectedTags.length === 0 ? 'default' : 'secondary'"
          :class="cn(
            'flex flex-col items-start gap-2 border p-3 text-left text-sm',
          )"
          @click="clearTags"
        >
          # All
        </Button>
      </li>
      <li v-for="tag in uniqueTags" class="items-center flex gap-1">
        <HoverCard :openDelay="100" :closeDelay="0" :delayDuration="0">
          <HoverCardTrigger as-child>
            <Button 
              :variant="selectedTags.includes(tag.name) ? 'default' : 'outline'"
              @click="toggleTag(tag.name)"
            >
              {{ tag.name }}
            </Button>
          </HoverCardTrigger>
          <HoverCardContent side="bottom" class="max-w-40 p-1">
            <div class="flex items-center gap-2 hover:bg-muted p-2 rounded-md" @click="openEditTagDialog(tag)">
              <Icon name="lucide:edit-3" />
              <span>Edit</span>
            </div>
          </HoverCardContent>
        </HoverCard>
      </li>
    </ul>
    
    <TagEditDialog
      v-if="currentTag"
      :open="editTagDialogOpen"
      :item="currentTag"
      @open-change="(open: boolean) => (editTagDialogOpen = open)"
      @saved="emit('tags:edited')"
    />
  </div>
</template>