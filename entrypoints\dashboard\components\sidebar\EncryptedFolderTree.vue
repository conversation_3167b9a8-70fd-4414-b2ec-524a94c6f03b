<script setup lang="ts">
import { shallowRef, watchEffect } from 'vue'
import { TreeRoot } from 'reka-ui'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { RouterLink } from 'vue-router'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'
import { useDialog } from '@/composables/useDialog';
import { useCrypto } from '@/composables/useCrypto';

const { isLocked, lock, hasStoredEncryption } = useCrypto()

const { currentUser } = useUserSession();

const { openDialog } = useDialog();

watchEffect((onCleanup) => {
  const dndFunction = combine(
    monitorForElements({
      onDrop(args) {
        const { location, source } = args        
        // didn't drop on anything
        if (!location.current.dropTargets.length)
          return

        console.warn('------- FolderTree TreeDND onDrop -------', source, location)

        const itemId = source.data.id as string
        const target = location.current.dropTargets[0]
        const targetId = target.data.id as string
        const selectedIds = source.data.selectedIds as string[]
        const sourceType = source.data.type as string
        const targetType = target.data.type as string

        const instruction: Instruction | null = extractInstruction(
          target.data,
        )

        // console.warn('------- FolderTree TreeDND onDrop -------', target, instruction, itemId, targetType)
        
        if (instruction !== null && instruction.type === 'make-child') {
          const parentId = targetType === 'folder' ? targetId : target.data.parentId as string;
          console.warn('------- FolderTree TreeDND onDrop make-child -------', instruction.type, itemId, parentId, sourceType, selectedIds)
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId })
          } else {
            // 循环执行 selectedIds 的 move 操作
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId })
            })
          }
        }
        if (instruction !== null && instruction.type === 'reorder-above') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          console.warn('--------- reorder-above ------- ', itemId, targetIndex, parentId)
          // 循环执行 selectedIds 的 move 操作
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex });
          } else {
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId, index: targetIndex });
            })
          }
        }
        if (instruction !== null && instruction.type === 'reorder-below') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          console.warn('--------- reorder-below ------- ', itemId, targetIndex, parentId, selectedIds)
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex + 1 });
          } else {
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId, index: targetIndex + 1 });
            })
          }
        }
        // if (instruction !== null) {
        //   items.value = updateTree(items.value, {
        //     type: 'instruction',
        //     instruction,
        //     itemId,
        //     targetId,
        //   }) ?? []
        // }
      },
    }),
  )

  onCleanup(() => {
    dndFunction()
  })
})

async function toggleEncryptedLock() {
  if (!currentUser.value) {
    openDialog('needToLogin')
    return;
  }

  if (isLocked.value) {
    // 检查是否有存储的加密配置
    const hasStored = await hasStoredEncryption();

    if (hasStored || currentUser.value?.encryptKey) {
      // 有加密配置，显示解锁对话框
      openDialog('unlockEncrypt');
    } else {
      // 新用户，显示设置密码对话框
      openDialog('setEncryptKey');
    }
  } else {
    lock()
  }
}
</script>
<template>
  <div class="flex flex-col gap-2 py-8">
    <div class="flex items-center justify-between gap-2">
      <h4 class="flex items-center text-xs font-bold text-muted-foreground">PROTECTED</h4>
      <div>
        <Button variant="ghost" size="icon" @click="toggleEncryptedLock">
          <Icon v-if="isLocked" name="lucide:lock" />
          <Icon v-else name="lucide:unlock" />
        </Button>
      </div>
    </div>
    <Button
      as-child
      variant="ghost"
      size="sm"
      class="justify-start text-base"
    >
      <RouterLink
        class="flex h-8 w-full gap-2 text-sm"
        :to="'/encrypted'"
      >
        <Icon
          name="lucide:lock"
          class="opacity-80"
        />
        Encrypted
        <span class="ml-auto" />
      </RouterLink>
    </Button>
  </div>
</template>
