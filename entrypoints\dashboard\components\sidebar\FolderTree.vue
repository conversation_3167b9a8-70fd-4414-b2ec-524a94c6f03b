<script setup lang="ts">
import { shallowRef, watchEffect } from 'vue'
import { TreeRoot } from 'reka-ui'
import FolderTreeItem from './FolderTreeItem.vue'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { 
  ContextMenu, 
  ContextMenuTrigger, 
  ContextMenuContent, 
  ContextMenuItem,
  ContextMenuSeparator,
 } from '@/components/ui/context-menu'
import EditFolderDialog from './EditFolderDialog.vue'
import NewFolderDialog from './NewFolderDialog.vue'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { useRouter } from 'vue-router'
import Icon from '@/components/Icon.vue'
import { useDialog } from '@/composables/useDialog'
import { findParentPath } from '@/utils/bookmark'

const { openDialog } = useDialog();

const router = useRouter()

const { currentUser } = useUserSession()
const bookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const folders = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const expandedIds = ref<string[]>([])

const props = withDefaults(defineProps<{
  currentFolderId: string
}>(), {})

// 注入 setSelectedFolderId 方法
const setSelectedFolderId = inject<(folderId: string) => void>('setSelectedFolderId')

const isContextMenuOpen = ref(false);
const showEditFolderDialog = ref(false)
const showNewFolderDialog = ref(false)
const confirmDeleteDialog = ref(false)

// 递归函数，过滤掉所有没有 children 的项目
function getFolders(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  return bookmarks.filter(bookmark => {
    if (bookmark.children) {
      bookmark.children = getFolders(bookmark.children); // 递归过滤子项
      return true;
    }
    return false;
  });
}

// 修改后的递归提取函数，限制层级为前两级
function defaultExpandedIds(nodes: chrome.bookmarks.BookmarkTreeNode[], currentLevel = 0) {
  return nodes.map(node => {
    const ids = [node.id];
    // 只在当前层级小于1时（即第一级和第二级）递归提取子节点
    if (currentLevel < 2 && node.children) {
      ids.push(...defaultExpandedIds(node.children, currentLevel + 1));
    }
    return ids;
  }).flat();
}

function handleExpandedUpdate(ids: string[]) {
  expandedIds.value = ids
}

watchEffect((onCleanup) => {
  const dndFunction = combine(
    monitorForElements({
      onDrop(args) {
        const { location, source } = args        
        // didn't drop on anything
        if (!location.current.dropTargets.length)
          return

        console.warn('------- FolderTree TreeDND onDrop -------', source, location)

        const itemId = source.data.id as string
        const target = location.current.dropTargets[0]
        const targetId = target.data.id as string
        const selectedIds = source.data.selectedIds as string[]
        const sourceType = source.data.type as string
        const targetType = target.data.type as string

        const instruction: Instruction | null = extractInstruction(
          target.data,
        )

        // console.warn('------- FolderTree TreeDND onDrop -------', target, instruction, itemId, targetType)
        
        if (instruction !== null && instruction.type === 'make-child') {
          const parentId = targetType === 'folder' ? targetId : target.data.parentId as string;
          console.warn('------- FolderTree TreeDND onDrop make-child -------', instruction.type, itemId, parentId, sourceType, selectedIds)
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId })
          } else {
            // 循环执行 selectedIds 的 move 操作
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId })
            })
          }
        }
        if (instruction !== null && instruction.type === 'reorder-above') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          console.warn('--------- reorder-above ------- ', itemId, targetIndex, parentId)
          // 循环执行 selectedIds 的 move 操作
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex });
          } else {
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId, index: targetIndex });
            })
          }
        }
        if (instruction !== null && instruction.type === 'reorder-below') {
          const targetIndex = target.data.index as number;
          const parentId = target.data.parentId as string;
          console.warn('--------- reorder-below ------- ', itemId, targetIndex, parentId, selectedIds)
          if (sourceType === 'sidebar-folder') {
            browser.bookmarks.move(itemId, { parentId: parentId, index: targetIndex + 1 });
          } else {
            selectedIds.forEach(id => {
              browser.bookmarks.move(id, { parentId: parentId, index: targetIndex + 1 });
            })
          }
        }
        // if (instruction !== null) {
        //   items.value = updateTree(items.value, {
        //     type: 'instruction',
        //     instruction,
        //     itemId,
        //     targetId,
        //   }) ?? []
        // }
      },
    }),
  )

  onCleanup(() => {
    dndFunction()
  })
})

async function setBookmarks() {
  const start = performance.now();
  bookmarks.value = await browser.bookmarks.getTree()
  folders.value = getFolders(bookmarks.value)
  const end = performance.now();
  console.log(`FolderTree getAllBookmark took ${(end - start).toFixed(0)} milliseconds`, bookmarks.value, folders.value); // 40 ms
}

async function performDeleteFolder() {
  const [folder] = await browser.bookmarks.get(props.currentFolderId);
  setSelectedFolderId?.(folder.parentId || '1');
  await browser.bookmarks.removeTree(props.currentFolderId);
  confirmDeleteDialog.value = false
}

function handleDeleteFolder() {
  confirmDeleteDialog.value = true;
}

// 修改后的批量打开函数
async function bulkOpenBookmark(type: 'tab' | 'window' | 'incognito') {
  try {
    const children = await browser.bookmarks.getChildren(props.currentFolderId);
    const urls = children
      ?.map(item => item.url)
      ?.filter((url): url is string => Boolean(url)) ?? [];
    
    if (urls.length === 0) return;
    
    if (type === 'tab') {
      urls.forEach(url => browser.tabs.create({ url }));
    } else {
      browser.windows.create({
        url: urls,
        incognito: type === 'incognito',
        state: 'maximized'
      });
    }
  } catch (error) {
    console.error('批量打开书签失败:', error);
  }
}

function handleShareFolder() {
  if (!currentUser.value) {
    openDialog('needToLogin')
    return;
  }

  openDialog('shareFolder', { defaultFolderId: props.currentFolderId })
}

// 添加watch监听currentFolderId变化
const isEncrypted = router.currentRoute.value.path.startsWith('/encrypted');
const isShared = router.currentRoute.value.path.startsWith('/shared');
const currentFolder = ref<chrome.bookmarks.BookmarkTreeNode>()
const currentFolderChildren = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
watch(() => props.currentFolderId, async (newId) => {
  if (isEncrypted || isShared) {
    return
  }
  const [folder] = await browser.bookmarks.get(newId)
  currentFolder.value = folder
  currentFolderChildren.value = await browser.bookmarks.getChildren(newId)
}, { immediate: true })

function handleRuntimeMessage(request: any) {
  console.warn('--------- sidebar FolderTree 收到消息 -----------!', request);
  if (request.type === 'bookmark-change') {
    setBookmarks();
  }
}

onMounted(async () => {
  await setBookmarks()
  expandedIds.value = defaultExpandedIds(folders.value)
  if (currentFolder.value?.parentId && !expandedIds.value.includes(currentFolder.value?.parentId)) {
    const parentPathIds = findParentPath(folders.value, currentFolder.value.id)
    expandedIds.value.push(...parentPathIds)
  }

  console.log(
    "Listener count before add:",
    browser.runtime.onMessage.hasListener(handleRuntimeMessage)
  );
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
});

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
});
</script>
<template>
  <div class="flex justify-center w-full">
    <TreeRoot v-slot="{ flattenItems }"
      class="w-full list-none select-none rounded-md text-sm font-semibold" 
      :items="folders[0]?.children"
      :get-key="(item) => item.id" 
      multiple 
      propagate-select 
      :expanded="expandedIds" 
      @update:expanded="handleExpandedUpdate"
    >
      <ContextMenu 
        v-model:open="isContextMenuOpen"
        @open-change="(open: boolean) => isContextMenuOpen = open"
      >
        <ContextMenuTrigger>
          <FolderTreeItem 
            v-for="item in flattenItems" 
            :key="item._id + item.index" 
            :item="item" 
            v-bind="item.bind"
            :currentFolderId="currentFolderId"
            class="flex items-center px-2 h-10 outline-hidden data-selected:bg-red-100"
            @select.prevent 
            @contextmenu.native="() => { setSelectedFolderId?.(item.value.id) }"
          />
        </ContextMenuTrigger>
        <ContextMenuContent 
          class="v-context-menu-content"
        >
          <ContextMenuItem 
            @select="showNewFolderDialog = !showNewFolderDialog"
          >
            {{ i18n.t('context_menu.newSubfolder') }}
          </ContextMenuItem>
          <ContextMenuItem 
            :disabled="currentFolderId === '1' || currentFolderId === '2'"
            @select="showEditFolderDialog = !showEditFolderDialog"
          >
            {{ i18n.t('context_menu.rename') }}
          </ContextMenuItem>
          <ContextMenuItem
            :disabled="currentFolderId === '1' || currentFolderId === '2'"
            @select="handleDeleteFolder"
          >
            {{ i18n.t('context_menu.delete') }}
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem
            @select="bulkOpenBookmark('tab')"
          >
            {{ i18n.t('context_menu.openAll', [currentFolderChildren?.length]) }}
          </ContextMenuItem>
          <ContextMenuItem
            @select="bulkOpenBookmark('window')"
          >
            {{ i18n.t('context_menu.openInNewWindow', [currentFolderChildren?.length]) }}
          </ContextMenuItem>
          <ContextMenuItem
            @select="bulkOpenBookmark('incognito')"
          >
            {{ i18n.t('context_menu.openIncognito', [currentFolderChildren?.length]) }}
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem
            @select="handleShareFolder"
          >
            <Icon name="lucide:share" class="mr-2 h-4 w-4" />
            {{ i18n.t('share.title') }}
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </TreeRoot>

    <NewFolderDialog
      :open="showNewFolderDialog"
      :current-folder-id="props.currentFolderId"
      @open-change="(open: boolean) => (showNewFolderDialog = open)"
    />

    <EditFolderDialog
      :open="showEditFolderDialog"
      :current-folder-id="props.currentFolderId"
      @open-change="(open: boolean) => (showEditFolderDialog = open)"
      @saved=""
    />

    <AlertDialog v-model:open="confirmDeleteDialog">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{{ i18n.t('dialog.confirm_delete') }}</AlertDialogTitle>
          <AlertDialogDescription>
            {{ i18n.t('dialog.delete_confirmation') }}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant="outline" @click="confirmDeleteDialog = false">
            {{ i18n.t('dialog.action.cancel') }}
          </Button>
          <Button variant="destructive" @click="performDeleteFolder()">
            {{ i18n.t('dialog.confirm_delete') }}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>
