<script setup lang="ts">
import { computed, h, nextTick, ref, render, watchEffect } from 'vue'
import { useRouter } from 'vue-router';
import { type FlattenedItem, TreeItem } from 'reka-ui'
import { draggable, dropTargetForElements, monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { type Instruction, type ItemMode, attachInstruction, extractInstruction } from '@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item'
import { pointerOutsideOfPreview } from '@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview'
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { Icon } from '@iconify/vue'
import { unrefElement } from '@vueuse/core'

const props = defineProps<{
  item: FlattenedItem<any>
  currentFolderId: string
}>()

const router = useRouter();

const elRef = ref()
const isDragging = ref(false)
const isDraggedOver = ref(false)
const isInitialExpanded = ref(false)
const instruction = ref<Extract<Instruction, { type: 'reorder-above' | 'reorder-below' | 'make-child' }> | null>(null)

const mode = computed(() => {
  if (props.item.hasChildren)
    return 'expanded'
  if (props.item.index + 1 === props.item.parentItem?.children?.length)
    return 'last-in-group'
  return 'standard'
})

function handleClick(event: MouseEvent) {
  router.push(`/browser?id=${props.item.value.id}`)
  event.stopImmediatePropagation();
}

watchEffect((onCleanup) => {
  const currentElement = unrefElement(elRef)
  
  if (!currentElement || props.item._id === "1" || props.item._id === "2")
    return

  const item = { ...props.item.value, level: props.item.level, id: props.item._id, type: 'sidebar-folder' }

  const expandItem = () => {
    if (!elRef.value?.isExpanded) {
      elRef.value?.handleToggle()
    }
  }

  const closeItem = () => {
    if (elRef.value?.isExpanded) {
      elRef.value?.handleToggle()
    }
  }

  const dndFunction = combine(
    draggable({
      element: currentElement,
      getInitialData: () => item,
      onDragStart: () => {
        isDragging.value = true
        isInitialExpanded.value = elRef.value?.isExpanded
        closeItem()
      },
      onDrop: () => {
        isDragging.value = false
        if (isInitialExpanded.value)
          expandItem()
      },
      onGenerateDragPreview({ nativeSetDragImage }) {
        setCustomNativeDragPreview({
          getOffset: pointerOutsideOfPreview({ x: '16px', y: '8px' }),
          render: ({ container }) => {
            return render(h(
              'div',
              { class: 'border border-4 border-blue-500 bg-blue-400 text-white rounded-md text-sm font-semibold px-3 py-1.5' },
              item.title,
            ), container)
          },
          nativeSetDragImage,
        })
      },
    }),

    dropTargetForElements({
      element: currentElement,
      getData: ({ input, element }) => {
        const data = { type: 'folder', id: item.id, index: item.index, parentId: item.parentId }

        return attachInstruction(data, {
          input,
          element,
          indentPerLevel: 16,
          currentLevel: props.item.level,
          mode: mode.value,
          block: [],
        })
      },
      canDrop: ({ source }) => {
        // 不能是自己的子节点和自己，这个在 chrome.bookmarks.move 中会控制，可以暂时不写
        return source.data.id !== item.id
      },
      onDrag: ({ self }) => {
        instruction.value = extractInstruction(self.data) as typeof instruction.value
      },
      onDragEnter: ({ source }) => {
        if (source.data.id !== item.id) {
          isDraggedOver.value = true
          expandItem()
        }
      },
      onDragLeave: () => {
        isDraggedOver.value = false
        instruction.value = null
      },
      onDrop: ({ location }) => {
        isDraggedOver.value = false
        instruction.value = null
        if (location.current.dropTargets[0].data.id === item.id) {
          nextTick(() => {
            expandItem()
          })
        }
      },
      getIsSticky: () => true,
    }),

    monitorForElements({
      canMonitor: ({ source }) => {
        return source.data.id !== item.id
      },
    }),
  )

  // Cleanup dnd function
  onCleanup(() => dndFunction())
})
</script>
<template>
  <TreeItem ref="elRef" 
    v-slot="{ isExpanded , handleToggle, handleSelect}" 
    :value="item.value" 
    :level="item.level"
    class="relative w-full hover:bg-muted cursor-pointer" 
    :class="{ 
      'opacity-50': isDragging, 'bg-muted' : currentFolderId === item.value.id && router.currentRoute.value.path !== '/encrypted' && router.currentRoute.value.path !== '/shared'
    }"
    @click.stop.prevent="handleClick"
  >
    <div v-for="level in item.level" :key="level" class="flex-shrink-0 w-2 min-w-2 h-full border-l ml-3 -z-10" :class="{'hidden' : level === 1}"></div>
    <div 
      v-if="item.value.children.length > 0"  
      class="hover:bg-gray-300/50 py-1 px-1 rounded-full cursor-default "
      @click.stop="handleToggle" >
      <Icon 
        icon="radix-icons:triangle-right" 
        class="h-4 w-4 transition"
        :class="{ 'rotate-90': isExpanded }" 
      />
    </div>
    <div v-else class="h-4 w-6 flex-shrink-0"></div>
    <div class="flex pl-1 items-center" > 
      <Icon icon="lucide:folder" class="h-4 w-4 transition" /> 
      <span class="pl-2 truncate">{{ item.value.title }}</span>
    </div>
    <div v-if="instruction" class="absolute h-full w-full top-0 border-primary -z-10" :style="{
      left: `${instruction?.currentLevel * instruction?.indentPerLevel}px`,
      width: `calc(100% - ${instruction?.currentLevel * instruction?.indentPerLevel}px)`,
    }" :class="{
      'border-b-2!': instruction?.type === 'reorder-below',
      'border-t-2!': instruction?.type === 'reorder-above',
      'bg-primary/20!': instruction?.type === 'make-child',
    }" />
  </TreeItem>
</template>
