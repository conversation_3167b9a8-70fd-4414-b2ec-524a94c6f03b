import { createApp } from 'vue';
import { createRouter, createWebHistory, createWebHashHistory  } from 'vue-router'
import "@/assets/tailwind.css";
import App from './App.vue';
// import initRxDB from '@/rxdb';

const app = createApp(App);

(async () => {
  const routes = [
    { path: "/", component: () => import("./pages/browser/index.vue") },
    { path: "/browser", component: () => import("./pages/browser/index.vue") },
    { path: "/encrypted", component: () => import("./pages/encrypted/index.vue") },
    { path: "/shared", component: () => import("./pages/shared/index.vue") },
  ]
  const router = createRouter({
    history: createWebHashHistory(),
    routes,
  })
  // const rxDBInstance = await initRxDB();
  // app.provide('db', rxDBInstance);
  app.use(router)
  app.mount('#app');
})();

// 与 background.ts 是隔离的上下文环境，需单独初始化数据库，rxdb 支持多实例 
// initRxDB()
//   .then(db => {
    // console.warn('dashboard - main -----------')
    // // 数据库就绪后注入并挂载
    // const routes = [
    //   { path: "/", component: () => import("./pages/browser/index.vue") },
    //   { path: "/browser", component: () => import("./pages/browser/index.vue") },
    //   { path: "/encrypted", component: () => import("./pages/encrypted/index.vue") },
    //   { path: "/shared", component: () => import("./pages/shared/index.vue") },
    // ]
    // const router = createRouter({
    //   history: createWebHashHistory(),
    //   routes,
    // })
    // // app.provide('db', db);
    // app.use(router)
    // app.mount('#app');
  // })
  // .catch(error => {
  //   console.error('Database initialization failed:', error);
  //   // 可添加错误提示UI
  //   app.mount('#app'); // 即使失败也挂载以显示错误界面
  // });