<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '@/components/ui/tags-input'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import Icon from '@/components/Icon.vue';
import cuid from 'cuid'
import { commonEmojis } from '@/utils'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
  selectedIds: string[]
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    // title: z.string().min(2),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const allTags = ref<string[]>([])
const searchTag = ref('')
const selectedTags = ref<string[]>([])
const editedExtra = ref<{ note: string, emoji: string }>({ note: '', emoji: '' })
const isPopoverOpen = ref(false)
const isEmojiPopoverOpen = ref(false)

const onSubmit = form.handleSubmit(async (values) => {
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    // 循环处理每个选中的书签
    for (const bookmarkId of props.selectedIds) {
      // 处理 tags 数据
      for (const tag of selectedTags.value) {
        const existTag = await rxdb.tags.findOne({
          selector: {
            browserAccountId: browserAccount.id,
            userId: currentUser.value?.id,
            name: tag
          }
        }).exec();
        if (existTag) {
          await existTag.patch({
            bookmarkIds: Array.from(new Set([...existTag.bookmarkIds, bookmarkId])),  // 使用循环中的bookmarkId
            updatedAt: new Date().toISOString(),
          });
        } else {
          await rxdb.tags.insert({
            id: cuid(),
            userId: currentUser.value?.id,
            browserAccountId: browserAccount.id,
            name: tag,
            bookmarkIds: [bookmarkId],  // 使用循环中的bookmarkId
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
        }
      }
    }

    emit('saved', {})
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    selectedTags.value = []
    editedExtra.value = { note: '', emoji: '' }
    const dbTags = await rxdb.tags.find({}).exec();
    allTags.value = dbTags.map((doc: any) => doc.name).sort((a, b) => a.localeCompare(b));
  }
})

</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ i18n.t('dialog.batch_add_tag_title') }}</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >

        <TagsInput
          v-model="selectedTags"
          class="w-full"
        >
          <TagsInputItem v-for="tag in selectedTags" :key="tag" :value="tag">
            <TagsInputItemText />
            <TagsInputItemDelete />
          </TagsInputItem>
          <TagsInputInput :placeholder="i18n.t('form.tags.placeholder')" />
        </TagsInput>

        <Command v-model="selectedTags" v-model:search-tag="searchTag" multiple class="border">
          <CommandInput placeholder="Search..." :auto-focus="false" />
          <CommandList>
            <CommandEmpty>{{ i18n.t('form.tags.noResults') }}</CommandEmpty>
            <CommandGroup class="flex flex-wrap gap-2 p-2">
              <CommandItem as="span"
                v-for="tag in allTags"
                :key="tag"
                :value="tag"
                class="bg-muted rounded px-2 py-1 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                @select.prevent="(ev: any) => {
                  if (typeof ev.detail.value === 'string') {
                    if (selectedTags.includes(ev.detail.value)) {
                      const index = selectedTags.indexOf(ev.detail.value)
                      selectedTags.splice(index, 1)
                    } else {
                      selectedTags.push(ev.detail.value)
                    }
                  }
                }"
              >
                {{ tag }}
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </form>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
          :disabled="selectedTags.length === 0"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
