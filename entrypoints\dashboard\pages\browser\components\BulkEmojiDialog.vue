<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import Icon from '@/components/Icon.vue';
import type { RxDatabase } from 'rxdb'
import cuid from 'cuid'
import { commonEmojis } from '@/utils'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
  selectedIds: string[]
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    // title: z.string().min(2),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const editedExtra = ref<{ note: string, emoji: string }>({ note: '', emoji: '' })
const isEmojiPopoverOpen = ref(false)

const onSubmit = form.handleSubmit(async (values) => {
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    // 循环处理每个选中的书签
    for (const bookmarkId of props.selectedIds) {
      // 处理 extra 数据
      const dbExtra = await rxdb.extras.findOne({
        selector: {
          bookmarkId: bookmarkId, 
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id
        }
      }).exec();
      if (dbExtra) {
        await dbExtra.patch({
          emoji: editedExtra.value.emoji,
          updatedAt: new Date().toISOString(),
        });
      } else {
        if (editedExtra.value.note.trim() !== "" || editedExtra.value.emoji) {
          await rxdb.extras.insert({
            id: cuid(),
            bookmarkId: bookmarkId,
            browserAccountId: browserAccount.id,
            userId: currentUser.value?.id,
            note: editedExtra.value.note.trim(),
            emoji: editedExtra.value.emoji,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
        }
      }
    }

    emit('saved', {})
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    toast.error(i18n.t('error.unknown'))
  }
})

watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    editedExtra.value = { note: '', emoji: '' }
  }
})

</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ i18n.t('dialog.batch_emoji_title') }}</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >
        <div id="emoji" class="flex items-center flex-col">
          <div class="flex flex-wrap p-2 gap-2">
            <Input
              :placeholder="i18n.t('form.emoji.placeholder')"
              v-model="editedExtra.emoji"
              ref="emojiInput"
            />
            <Button
              type="button"
              v-for="emoji in commonEmojis"
              :key="emoji"
              variant="ghost"
              size="sm"
              class="text-xl h-10 w-10 p-0 rounded-full hover:bg-accent"
              @click.stop="editedExtra.emoji = emoji;"
            >
              {{ emoji }}
            </Button>
            
          </div>
          <!-- <a href="https://getemoji.com/" target="_blank" class="text-sm text-muted-foreground">getemoji.com</a> -->
        </div>
      </form>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
