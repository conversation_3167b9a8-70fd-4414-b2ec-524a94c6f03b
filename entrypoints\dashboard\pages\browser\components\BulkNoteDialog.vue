<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import cuid from 'cuid'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
  selectedIds: string[]
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    // title: z.string().min(2),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})


const editedExtra = ref<{ note: string, emoji: string }>({ note: '', emoji: '' })

const onSubmit = form.handleSubmit(async (values) => {
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    // 循环处理每个选中的书签
    for (const bookmarkId of props.selectedIds) {
      // 处理 extra 数据
      const dbExtra = await rxdb.extras.findOne({
        selector: {
          bookmarkId: bookmarkId, 
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id
        }
      }).exec();
      if (dbExtra) {
        await dbExtra.patch({
          note: editedExtra.value.note.trim(),
          updatedAt: new Date().toISOString(),
        });
      } else {
        if (editedExtra.value.note.trim() !== "" || editedExtra.value.emoji) {
          await rxdb.extras.insert({
            id: cuid(),
            bookmarkId: bookmarkId,
            browserAccountId: browserAccount.id,
            userId: currentUser.value?.id,
            note: editedExtra.value.note.trim(),
            emoji: editedExtra.value.emoji,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
        }
      }
    }

    emit('saved', {})
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    editedExtra.value = { note: '', emoji: '' }
  }
})

</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ i18n.t('dialog.batch_note_title') }}</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="note"
          v-model="editedExtra.note"
        >
          <FormItem>
            <FormControl>
              <Textarea
                class="bg-background"
                :placeholder="i18n.t('form.note.placeholder')"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
