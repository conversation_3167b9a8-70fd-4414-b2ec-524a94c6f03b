<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { user } = useUserSession()

const props = defineProps<{
  open: boolean
  selectedIds: string[]
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    // title: z.string().min(2),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const allTags = ref<string[]>([])
const selectedTags = ref<string[]>([])

const onSubmit = form.handleSubmit(async (values) => {
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    // 循环处理每个选中的书签
    for (const bookmarkId of props.selectedIds) {
      // 处理 tags 数据
      for (const tag of selectedTags.value) {
        const existTag = await rxdb.tags.findOne({
          selector: {
            browserAccountId: browserAccount.id,
            userId: currentUser.value?.id,
            name: tag
          }
        }).exec();
        if (existTag) {
          // 把 selectedIds 从 existTag.bookmarkIds 中移除  
          await existTag.patch({
            bookmarkIds: existTag.bookmarkIds.filter((id: string) => id !== bookmarkId),
            updatedAt: new Date().toISOString(),
          });
        }
      }
    }

    emit('saved', {})
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

function toggleTag(tagName: string) {
  const index = selectedTags.value.indexOf(tagName);
  if (index === -1) {
    selectedTags.value = [...selectedTags.value, tagName];
  } else {
    selectedTags.value = selectedTags.value.filter(t => t !== tagName);
  }
}

watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    // 从 db 中获取所有与 selectedIds 相关的 tags
    const browserAccount = await browser.identity.getProfileUserInfo()
    const dbTags = await rxdb.tags.find({
      selector: {
        browserAccountId: browserAccount.id,
        userId: user.value?.id,
        bookmarkIds: { $in: props.selectedIds }
      }
    }).exec();
    allTags.value = dbTags.map((doc: any) => doc.name).sort((a, b) => a.localeCompare(b));
    // 从 db 中获取所有与 selectedIds 相关的 tags
    selectedTags.value = []
  }
})

</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ i18n.t('dialog.batch_remove_tag_title') }}</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>

      <Badge 
        v-for="tag in allTags"
        :key="tag"
        :variant="selectedTags.includes(tag) ? 'destructive' : 'secondary'"
        size="xs"
      >
        <span @click="toggleTag(tag)" class="flex-1">{{ tag }}</span>
      </Badge>
      <div v-if="allTags.length === 0" class="mt-2 text-sm text-muted-foreground">
        {{ i18n.t('dialog.no_exist_tags') }}
      </div>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
          @click="onSubmit"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
