<script setup lang="ts">
import { ref } from 'vue';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button';

const props = defineProps<{
  open: boolean
  selectedIds: string[]
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  deleted: [item: object | null]
}>()

const handleDelete = async () => {
  try {
    const items = await Promise.all(
      props.selectedIds.map(id => 
        browser.bookmarks.get(id).then(results => results[0])
      )
    );

    await Promise.all(
      items.map(item => {
        if (item.url) {
          return browser.bookmarks.remove(item.id);
        } else {
          return browser.bookmarks.removeTree(item.id);
        }
      })
    );

    emit('deleted', {})
    emit('openChange', false)
  } catch (error) {
    console.error('Delete failed:', error);
  }
};
</script>

<template>
  <AlertDialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Confirm Deletion of {{ selectedIds.length }} Items</AlertDialogTitle>
        <AlertDialogDescription>
          This action will permanently delete the selected bookmarks/folders ({{ selectedIds.length }} items). This operation cannot be undone!
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <Button variant="outline" @click="emit('openChange', false)">
          Cancel
        </Button>
        <Button variant="destructive" @click="handleDelete()">
          Confirm Delete
        </Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

