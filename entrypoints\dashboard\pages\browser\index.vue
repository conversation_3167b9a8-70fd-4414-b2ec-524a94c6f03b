<script setup lang="ts">
import { use<PERSON>oute, useRouter } from 'vue-router';
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { refDebounced } from '@vueuse/core'
import Icon from '@/components/Icon.vue'
import { onClickOutside } from '@vueuse/core'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from '@/components/ui/context-menu'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import ListItem from '../../components/ListItem.vue'
import FolderBreadcrumbs from '@/components/FolderBreadcrumbs.vue'
import ViewOptions from '../../components/ViewOptions.vue'
import TagsList from '../../components/TagsList.vue'
import FolderEditDialog from './components/FolderEditDialog.vue'
import BookmarkEditDialog from './components/BookmarkEditDialog.vue'
import BulkAddTagDialog from './components/BulkAddTagDialog.vue'
import BulkRemoveTagDialog from './components/BulkRemoveTagDialog.vue'
import BulkNoteDialog from './components/BulkNoteDialog.vue'
import BulkEmojiDialog from './components/BulkEmojiDialog.vue'
import SortingOptions from '../../components/SortingOptions.vue'
import { countBookmarksAndFoldersById, getFlattenedSubTree, sortBookmarks } from '@/utils/bookmark'
import ConfirmDeleteDialog from './components/ConfirmDeleteDialog.vue'
import { select, selectAll } from '@/utils/multiselect'
import { rxDBInstance as rxdb } from '@/rxdb/index'
import { Subscription } from 'rxjs'

const route = useRoute();
const router = useRouter();

// dialogs
const editBookmarkDialogOpen = ref(false)
const editFolderDialogOpen = ref(false)
const bulkAddTagDialogOpen = ref(false)
const bulkRemoveTagDialogOpen = ref(false)
const bulkNoteDialogOpen = ref(false)
const bulkEmojiDialogOpen = ref(false)
const confirmDeleteDialogOpen = ref(false)

const currentFolderId = ref<string>('')  // 初始值直接使用 route.query.id
const currentFolder = ref<chrome.bookmarks.BookmarkTreeNode>();
watch(
  () => route.query.id,
  async (newId) => {
    currentFolderId.value = (newId as string) || '1';
    currentFolder.value = (await browser.bookmarks.get(currentFolderId.value))[0];
    if (route.query.bookmarkId) {
      selectedIds.value = [route.query.bookmarkId as string]
    }
  }, { immediate: true }
);

const selectedTags = ref<string[]>([]);
const search = ref<string>('')
const searchDebounced = refDebounced(search, 35)
const includeSubfolders = ref<boolean | 'indeterminate'>(true)

// 实际在页面上渲染的书签列表(包括深层搜索结果或者仅是当前文件夹书签)，分批加载，以提高渲染性能
const listBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const currentLevelBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const includedSubFoldersBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const subFolderCount = ref(0)
const subBookmarkCount = ref(0)
async function refreshBookmarksData() {
  currentLevelBookmarks.value = await browser.bookmarks.getChildren(currentFolderId.value)
  includedSubFoldersBookmarks.value = await getFlattenedSubTree(currentFolderId.value)
  const { folderCount, bookmarkCount } = await countBookmarksAndFoldersById(currentFolderId.value);
  subFolderCount.value = folderCount
  subBookmarkCount.value = bookmarkCount
}

// 多选
const pivotId = ref<string | null>(null)
const selectedIds = ref<string[]>([])
const selectedItem = computed(() => {
  return listBookmarks.value.find(item => item.id === selectedIds.value[0])
})

// 单击时选中一个书签，如果按住 ctrl 或 meta 键，则为多选
// 如果按住 shift 键，则选中从 pivotId 到 itemId 之间的所有书签
function handleClick(itemId: string, e?: PointerEvent) {
  if (e?.ctrlKey || e?.metaKey) {
    const [pivot, newIds] = select({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      id: itemId
    })
    pivotId.value = pivot
    selectedIds.value = newIds
  } else if (e?.shiftKey) {
    const selected = selectAll({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      ids: listBookmarks.value.map(item => item.id),
      id: itemId
    })
    selectedIds.value = selected
  } else {
    if (selectedIds.value.includes(itemId) && selectedIds.value.length === 1) {
      pivotId.value = null
      selectedIds.value = []
    } else {
      pivotId.value = itemId
      selectedIds.value = [itemId]
    }
  }
}

// 双击打开书签或者跳转文件夹
function handleDblclick(item: chrome.bookmarks.BookmarkTreeNode) {
  if (item.url) {
    browser.tabs.create({ url: item.url })
    selectedIds.value = [item.id]
  } else {
    router.replace({ query: { ...route.query, id: item.id } })
  }
}

// 实现类似 chrome 书签管理器的批量打开书签功能，循环打开 selectedIds 中的书签
// type 分别为普通打开，在新窗口中打开，以及在无痕式窗口中打开
function bulkOpenBookmark(type: 'tab' | 'window' | 'incognito') {
  const urls = selectedIds.value
    .map(id => listBookmarks.value.find(item => item.id === id)?.url)
    .filter(Boolean) as string[];
  if (urls.length === 0) return;
  if (type === 'tab') {
    urls.forEach(url => browser.tabs.create({ url }));
  } else {
    // 在新窗口/无痕窗口一次性打开所有标签页
    browser.windows.create({
      url: urls,
      incognito: type === 'incognito',
      state: 'maximized'
    });
  }
}

// 添加专门的右键处理函数
function handleRightClick(e: MouseEvent, itemId: string) {  
  // 如果当前右键项不在选中列表中
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
    pivotId.value = itemId
  }
}

function handleEdit(id?: string) {
  if (id) {
    selectedIds.value = [id]
  }
  if (selectedItem?.value?.url) {
    editBookmarkDialogOpen.value = true
  } else {
    editFolderDialogOpen.value = true
  }
}

function isSelected(id: string) {
  return selectedIds.value.includes(id)
}

const multiSelectTarget = ref<HTMLElement | null>(null)
const contextMenuOpen = ref(false);
onClickOutside(multiSelectTarget, () => {
  if (
    !contextMenuOpen.value && 
    !editFolderDialogOpen.value && 
    !editBookmarkDialogOpen.value && 
    !confirmDeleteDialogOpen.value &&
    !bulkAddTagDialogOpen.value &&
    !bulkRemoveTagDialogOpen.value &&
    !bulkNoteDialogOpen.value &&
    !bulkEmojiDialogOpen.value
  ) {
    selectedIds.value = []
    pivotId.value = null
  }
}, {ignore: []});

function itemDragStart(itemId: string) {
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
  }
}

// 确认删除
const handleDeleteSuccess = () => {
  selectedIds.value = [];
};

// 分批渲染函数
const animationFrameId = ref<number | null>(null);
async function renderInBatches(items: chrome.bookmarks.BookmarkTreeNode[]) {
  const start = performance.now();
  // 取消之前的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }

  const sortedBookmarks = sortBookmarks(items, sortBy.value)
  let index = 0;
  const batchSize = 15;
  let currentBatch: chrome.bookmarks.BookmarkTreeNode[] = [];
  // 清空当前列表 - 会导致闪烁
  listBookmarks.value = [];

  function processBatch() {
    const end = Math.min(index + batchSize, sortedBookmarks.length);
    for (; index < end; index++) {
      currentBatch.push(sortedBookmarks[index]);
    }
    
    listBookmarks.value = [...currentBatch];
    
    if (index < sortedBookmarks.length) {
      animationFrameId.value = requestAnimationFrame(processBatch);
    } else {
      const end = performance.now();
      console.log(`All items processed took ${(end - start).toFixed(0)} milliseconds`);
      animationFrameId.value = null;
    }
  }

  processBatch();
}

// 可选的排序方式
const sortBy = ref<{ field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }>({
  field: 'default',
  direction: 'asc'
});

// 修改排序
function handleSort(field: 'default' | 'title' | 'dateAdded' | 'url' | 'id') {
  if (sortBy.value.field === field) {
    // 切换排序方向
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc';
  } else {
    // 切换排序字段并重置方向
    sortBy.value.field = field;
    sortBy.value.direction = 'asc';
  }
  renderInBatches(listBookmarks.value); // 重新加载数据应用排序
}

// 获取相关 extras 和 tags
const extras = ref<any[]>([])
const uniqueTags = ref<any[]>([]);
async function getUniqueTagsAndExtras() {
  const start = performance.now();
  let allBookmarks: chrome.bookmarks.BookmarkTreeNode[] = []
  if (includeSubfolders.value) {
    allBookmarks = includedSubFoldersBookmarks.value
  } else {
    allBookmarks = currentLevelBookmarks.value
  }
  // 获取所有 flattenedSubBookmarks 的 bookmarkIds，x 排除那些没有 url 的节点（文件夹）
  const allBookmarksIds =  allBookmarks
    // .filter(item => item.url)
    .map(item => item.id)
  // 2. 查询所有包含这些书签ID的标签
  uniqueTags.value = await rxdb.tags.find({
    selector: {
      bookmarkIds: {
        $elemMatch: { $in: allBookmarksIds }
      }
    }
  }).exec();
  extras.value = await rxdb.extras.find({
    selector: {
      bookmarkId: { $in: allBookmarksIds }
    }
  }).exec();
  const end = performance.now();
  console.warn(`getUniqueTagsAndExtras took ${(end - start).toFixed(0)} milliseconds`)
}

// 当前是否是搜索状态，或者有标签选中
const isFiltered = computed(() => search.value.length > 0 || selectedTags.value.length > 0)

// 重置搜索
function resetSearch() {
  search.value = ''
  selectedTags.value = []
}

// 根据标签过滤书签
async function filterByTags(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  if (selectedTags.value.length === 0) return bookmarks;
  // Promise.all() 在这里是 必要且高效 的，它的作用是并行查询和合并结果
  const results = await Promise.all(
    selectedTags.value.map(tagName => 
      rxdb.tags.findOne({ selector: { name: tagName } }).exec()
    )
  );
  const intersection = results
    .filter(Boolean)
    .map((doc: any) => doc.bookmarkIds || [])
    .reduce((a, b) => a.filter((id: string) => b.includes(id)));
  return bookmarks.filter(item => intersection.includes(item.id));
}

// 根据关键词过滤书签，如果是搜索，最多返回 100 个结果
async function filterByKeyword(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  if (!searchDebounced.value) return bookmarks;
  const lowerTerm = searchDebounced.value.toLowerCase();
  const keywordFilteredIds = bookmarks.filter(item => {
    const title = item.title?.toLowerCase() || '';
    const url = item.url?.toLowerCase() || '';
    return title.includes(lowerTerm) || url.includes(lowerTerm);
  }).map(item => item.id);
  // 搜索 db.extra 表中的 note 和 emoji 字段, 并返回匹配的 extra 表数据
  const extraResults = await rxdb.extras.find({
    selector: {
      $or: [
        { note: { $regex: searchDebounced.value } },
        { emoji: { $regex: searchDebounced.value } }
      ]
    }
  }).exec();
  // 提取匹配的 extra 表数据中的 bookmarkId 字段合并为 ids 合集并去重
  const extraBookmarkIds = Array.from(new Set(
    extraResults
      .filter((doc: any) => doc.bookmarkId) // 过滤掉空值
      .map((doc: any) => doc.bookmarkId)
  ));
  // 合并 keywordFilteredIds 和 extraBookmarkIds 并去重
  const combinedIds = [...new Set([...keywordFilteredIds, ...extraBookmarkIds])];
  const filtered = bookmarks.filter(item => combinedIds.includes(item.id));
  return filtered.slice(0, 100)
}

// 更新书签列表
async function updateBookmarkList() {
  const start = performance.now();
  await refreshBookmarksData()
  let allBookmarks: chrome.bookmarks.BookmarkTreeNode[] = []
  // 非搜索时仅显示当前层级书签，但搜索时可能要搜索深层书签
  if (isFiltered.value && includeSubfolders.value) {
    allBookmarks = includedSubFoldersBookmarks.value
  } else {
    allBookmarks = currentLevelBookmarks.value
  }
  
  const filteredByTags = await filterByTags(allBookmarks);
  const filteredByKeyword = await filterByKeyword(filteredByTags);
  renderInBatches(filteredByKeyword);
  getUniqueTagsAndExtras()
  const end = performance.now();
  console.warn('--------- updateBookmarkList -----------', (end - start).toFixed(0), 'ms')
}

watch([currentFolderId, searchDebounced, selectedTags, includeSubfolders], updateBookmarkList, { immediate: true });

// 监听浏览器消息，当 bookmark 被修改时重新加载当前层级书签列表
function handleRuntimeMessage(request: { type: string }) {
  console.warn('--------- manage list 收到消息 --- browser -----------!', request)
  if (request.type === 'bookmark-change') {
    updateBookmarkList()
  }
}

// 订阅实时查询
const unsubscribeExtras = ref<Subscription | null>(null);
const unsubscribeTags = ref<Subscription | null>(null);
onMounted(async () => {
  // 先移除旧的 Listener，避免重复添加
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
  unsubscribeExtras.value = rxdb.extras.find({}).$.subscribe((results) => {
    console.warn('--------- extras 变化 -----------', results)
    getUniqueTagsAndExtras()
  });
  unsubscribeTags.value = rxdb.tags.find({}).$.subscribe((results) => {
    console.warn('--------- tags 变化 -----------', results)
    getUniqueTagsAndExtras()
  });
});

onUnmounted(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
  }
  if (unsubscribeExtras.value) {
    unsubscribeExtras.value.unsubscribe();
  }
  if (unsubscribeTags.value) {
    unsubscribeTags.value.unsubscribe();
  }
});
</script>

<template>
  <div>
    <div class="flex justify-between mt-4 items-center">
      <div class="flex flex-col space-y-1 items-start">
        <FolderBreadcrumbs :folderId="currentFolderId" :maxCrumbs="5" />
        <Tooltip>
          <TooltipTrigger as-child>
            <h2 class="text-2xl font-bold">{{ currentFolder?.title }}</h2>
          </TooltipTrigger>
          <TooltipContent>
            <p class="text-sm">
              <span class="font-bold">{{ subFolderCount }}</span> sub folders, 
              <span class="font-bold">{{ subBookmarkCount }}</span> bookmarks, 
              <span class="font-bold">{{ uniqueTags.length }}</span> tags.
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
    
    <div class="flex items-center justify-between mt-4">
      <div class="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter bookmarks ..."
          v-model="search"
          class="h-10 w-[220px] lg:w-[350px] rounded-full pl-4 bg-secondary pt-0 focus-visible:ring-2"
        />
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="includeSubfolders" 
            v-model="includeSubfolders"
            class="h-4 w-4"
          />
          <label 
            for="includeSubfolders" 
            class="text-sm font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Sub folders
          </label>
        </div>
        <Button
          v-if="isFiltered"
          variant="ghost"
          class="h-8 px-2 lg:px-3"
          @click="resetSearch()"
        >
          Reset
          <Icon name="radix-icons:cross-2" />
        </Button>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          class="h-8 px-2 lg:px-3"
        >
          <Icon name="lucide:share" /> Share
        </Button>
        <SortingOptions 
          :sort-by="sortBy"
          @sort="handleSort"
        />
        <ViewOptions />
      </div>
    </div>

    <TagsList
      :unique-tags="uniqueTags"
      :selected-tags="selectedTags"
      @update:selected-tags="selectedTags = $event"
    />

    <div v-if="listBookmarks.length" class="rounded-md border mt-4 py-2" ref="multiSelectTarget">
      <ContextMenu 
          v-model:open="contextMenuOpen"
          @open-change="(open: boolean) => contextMenuOpen = open"
        >
          <ContextMenuTrigger>
          <ul>
            <ListItem
              v-for="item in listBookmarks"
              :key="item.id"
              :data-state="isSelected(item.id) && 'selected'"
              @contextmenu="(e: MouseEvent) => handleRightClick(e, item.id)"
              @click.stop="(e: PointerEvent) => handleClick(item.id, e)"
              @dblclick="handleDblclick(item)"
              class="transition-none data-[state=selected]:bg-muted"
              :item="item"
              :selectedIds="selectedIds"
              :tags="uniqueTags.filter(tag => tag.bookmarkIds.includes(item.id))"
              :extra="extras.find(extra => extra.bookmarkId === item.id)"
              @drag-start="itemDragStart"
              @open-context-menu="contextMenuOpen = true"
              @delete="(id) => { handleClick(id); confirmDeleteDialogOpen = true }"
              @edit="(id) => handleEdit(id)"
            >
            </ListItem>
          </ul>
          </ContextMenuTrigger>
          <ContextMenuContent 
            class="v-context-menu-content"
          >
            <ContextMenuItem 
              :disabled="selectedIds.length > 1"
              @select="handleEdit()"
            >
              {{ i18n.t('context_menu.edit') }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="confirmDeleteDialogOpen = true"
            >
              {{ i18n.t('context_menu.deleteWithCount', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuSub>
              <ContextMenuSubTrigger>
                {{ i18n.t('context_menu.bulkActions') }}
              </ContextMenuSubTrigger>
              <ContextMenuSubContent>
                <ContextMenuItem 
                  @select="bulkAddTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkAddTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkRemoveTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkRemoveTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkNoteDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkNote') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkEmojiDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkEmoji') }}
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
            <ContextMenuSeparator />
            <ContextMenuItem 
              @select="bulkOpenBookmark('tab')"
            >
              {{ i18n.t('context_menu.openAll', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('window')"
            >
              {{ i18n.t('context_menu.openInNewWindow', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('incognito')"
            >
              {{ i18n.t('context_menu.openIncognito', [selectedIds.length]) }}
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
    </div>
    <div v-else class="flex flex-col h-48 text-center rounded-md border mt-4 justify-center gap-2">
      <h3 class="text-lg font-semibold">
        {{ i18n.t('options.list.noResults') }}
      </h3>
      <p class="text-sm text-muted-foreground">
        {{ i18n.t('options.list.noResultsDescription') }}
      </p>
    </div>

    <FolderEditDialog
      :open="editFolderDialogOpen"
      :item="selectedItem"
      @open-change="(open: boolean) => (editFolderDialogOpen = open)"
    />

    <BookmarkEditDialog
      :open="editBookmarkDialogOpen"
      :item="selectedItem"
      @open-change="(open: boolean) => (editBookmarkDialogOpen = open)"
    />

    <BulkAddTagDialog
      :open="bulkAddTagDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open: boolean) => (bulkAddTagDialogOpen = open)"
    />

    <BulkRemoveTagDialog
      :open="bulkRemoveTagDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open: boolean) => (bulkRemoveTagDialogOpen = open)"
    />

    <BulkNoteDialog
      :open="bulkNoteDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open: boolean) => (bulkNoteDialogOpen = open)"
    />

    <BulkEmojiDialog
      :open="bulkEmojiDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open: boolean) => (bulkEmojiDialogOpen = open)"
    />

    <ConfirmDeleteDialog
      :open="confirmDeleteDialogOpen"
      :selectedIds="selectedIds"
      @open-change="(open: boolean) => (confirmDeleteDialogOpen = open)"
      @deleted="handleDeleteSuccess"
    />
  </div>
</template>
