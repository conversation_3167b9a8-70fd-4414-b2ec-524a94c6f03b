<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import AutosizeTextarea from '@/components/AutosizeTextarea.vue'
import { Textarea } from '@/components/ui/textarea'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '@/components/ui/tags-input'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import Icon from '@/components/Icon.vue';
import cuid from 'cuid'
import { commonEmojis, faviconURL } from '@/utils'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
  item: chrome.bookmarks.BookmarkTreeNode | undefined
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    url: z.string().min(2),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

const isPopoverOpen = ref(false)
const isEmojiPopoverOpen = ref(false)

// init Extra
const existExtra = ref<{ id?: string; note: string; emoji: string; createdAt?: string } | null>(null)
const editedExtra = ref({
  note: "",
  emoji: ""
})
async function resetExtra() {
  if (!props.item) return
  const browserAccount = await browser.identity.getProfileUserInfo()
  // 是否需要加入 userId 的判断条件?
  existExtra.value = await rxdb.extras.findOne({
    selector: {
      bookmarkId: props.item!.id,
      browserAccountId: browserAccount.id,
      userId: currentUser.value?.id
    }
  }).exec();

  if (existExtra.value) {
    editedExtra.value = {
      note: existExtra.value.note,
      emoji: existExtra.value.emoji
    }
  } else {
    editedExtra.value = {
      note: "",
      emoji: ""
    }
  } 
}

// init Tags
const allTagNames = ref<string[]>([])
const searchTag = ref('')
const currentTagNames = ref<string[]>([])
async function resetTags() {
  if (!props.item) return 
  const dbTags = await rxdb.tags.find({}).exec();
  allTagNames.value = dbTags.map((doc: any) => doc.name);
  const bookmarkTags = await rxdb.tags.find({
    selector: {
      bookmarkIds: {
        $in: [props.item.id]
      },
    }
  }).exec();
  currentTagNames.value = bookmarkTags.map((doc: any) => doc.name);
}

const onSubmit = form.handleSubmit(async (values) => {
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    // 更新书签数据
    const updatedItem = await browser.bookmarks.update(props.item!.id, { title: values.title, url: values.url })
    
    // 处理 extra 数据
    if (existExtra.value) {
      await rxdb.extras.findOne({ selector: { id: existExtra.value.id } }).patch({
        note: editedExtra.value.note.trim(),
        emoji: editedExtra.value.emoji,
        updatedAt: new Date().toISOString(),
      });
    } else {
      if (editedExtra.value.note.trim() !== "" || editedExtra.value.emoji) {
        await rxdb.extras.insert({
          id: cuid(),
          bookmarkId: props.item?.id,
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          note: editedExtra.value.note.trim(),
          emoji: editedExtra.value.emoji,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
      }
    }

    // 收集所有异步操作，处理标签的删除和新增
    const promises = []
    const currentBookmarkId = props.item!.id
    // 1. 获取当前书签已有的所有标签
    const existingTags = await rxdb.tags.find({
      selector: {
        bookmarkIds: { $in: [currentBookmarkId] }
      }
    }).exec();
    // 2. 处理需要移除的标签
    const tagsToRemove = existingTags.filter((tag: any) => !currentTagNames.value.includes(tag.name))
    for (const tag of tagsToRemove) {
      
      const updatedBookmarkIds = tag.bookmarkIds.filter((id: string) => id !== currentBookmarkId)
      await rxdb.tags.findOne({ selector: { id: tag.id } }).patch({
        bookmarkIds: updatedBookmarkIds,
        updatedAt: new Date().toISOString()
      })
    }
    // 3. 处理需要新增的标签
    const tagsToAdd = currentTagNames.value.filter((tagName: string) => 
      !existingTags.some((t: any) => t.name === tagName)
    )
    for (const tagName of tagsToAdd) {
      const existTag = await rxdb.tags.findOne({
        selector: {
          name: tagName,
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id
        }
      }).exec();

      if (existTag) {
        await existTag.patch({
          bookmarkIds: [...new Set([...existTag.bookmarkIds, currentBookmarkId])],
          updatedAt: new Date().toISOString()
        });
      } else {
        await rxdb.tags.insert({
          id: cuid(),
          name: tagName,
          bookmarkIds: [currentBookmarkId],
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
      }
    }
      
    // 所有操作完成后触发事件
    emit('saved', updatedItem)
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

// 添加监听器，当对话框打开时获取数据
watch(() => props.open, async (isOpen) => {
  if (isOpen && props.item) {   
    // 如果父组件没有传入item，则主动获取
    resetExtra()
    resetTags()
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle><img class="h-4 w-4" :src="faviconURL(item?.url || i18n.t('dialog.edit_bookmark_title'))" alt=""></DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
          :value="item?.title"
        >
          <FormItem>
            <FormControl>
              <AutosizeTextarea 
                v-bind="componentField" 
                :disableNewlines="true"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <FormField
          v-slot="{ componentField }"
          name="url"
          :value="item?.url"
        >
          <FormItem>
            <FormControl>
              <AutosizeTextarea 
                v-bind="componentField" 
                :disableNewlines="true"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <Popover v-model:open="isPopoverOpen" class="min-w-(--reka-select-trigger-width) w-(--reka-select-trigger-width)">
          <PopoverTrigger as-child>
            <TagsInput
              v-model="currentTagNames"
              @focus="isPopoverOpen = true"
              @blur="isPopoverOpen = false"
              class="w-full"
            >
              <TagsInputItem v-for="tag in currentTagNames" :key="tag" :value="tag">
                <TagsInputItemText />
                <TagsInputItemDelete />
              </TagsInputItem>
              <TagsInputInput :placeholder="i18n.t('form.tags.placeholder')" />
            </TagsInput>
          </PopoverTrigger>

          <PopoverContent @open-auto-focus.prevent class="p-0 w-(--reka-popover-trigger-width)" @pointerdownOutside="isPopoverOpen = false"  >
            <Command v-model="currentTagNames" v-model:search-tag="searchTag" multiple>
              <CommandInput placeholder="Search..." :auto-focus="false" />
              <CommandList>
                <CommandEmpty>{{ i18n.t('form.tags.noResults') }}</CommandEmpty>
                <CommandGroup class="flex flex-wrap gap-2 p-2">
                  <CommandItem
                    v-for="tag in allTagNames"
                    :key="tag"
                    :value="tag"
                    class="bg-muted rounded px-2 py-1 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                    @select.prevent="(ev: any) => {
                      if (typeof ev.detail.value === 'string') {
                        if (currentTagNames.includes(ev.detail.value)) {
                          const index = currentTagNames.indexOf(ev.detail.value)
                          currentTagNames.splice(index, 1)
                        } else {
                          currentTagNames.push(ev.detail.value)
                        }
                      }
                    }"
                  >
                    {{ tag }}
                  </CommandItem>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <FormField
          v-slot="{ componentField }"
          name="note"
          v-model="editedExtra.note"
        >
          <FormItem>
            <FormControl>
              <Textarea
                class="bg-background"
                :placeholder="i18n.t('form.note.placeholder')"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        
        <div id="emoji" class="flex items-center">
          <Popover v-model:open="isEmojiPopoverOpen">
            <PopoverTrigger as-child>
              <Button 
                variant="outline" 
                class="justify-center"
                type="button"
              >
                <span v-if="editedExtra.emoji" class="text-base">{{ editedExtra.emoji }}</span>
                <Icon v-else name="radix-icons:face" class="w-4 h-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent 
              class="w-[300px] p-0" 
              @open-auto-focus.prevent
            >
              <div class="flex flex-wrap p-2 gap-2">
                <Input
                  :placeholder="i18n.t('form.emoji.placeholder')"
                  v-model="editedExtra.emoji"
                  ref="emojiInput"
                />
                <Button
                  v-for="emoji in commonEmojis"
                  :key="emoji"
                  variant="ghost"
                  size="sm"
                  class="text-xl h-10 w-10 p-0 rounded-full hover:bg-accent"
                  @click.stop="editedExtra.emoji = emoji; isEmojiPopoverOpen = false"
                >
                  {{ emoji }}
                </Button>
              </div>
              <!-- <a href="https://getemoji.com/" target="_blank" class="text-sm text-muted-foreground">getemoji.com</a> -->
            </PopoverContent>
          </Popover>
        </div>

      </form>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
