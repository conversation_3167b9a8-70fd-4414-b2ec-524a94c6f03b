<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '@/components/ui/tags-input'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import Icon from '@/components/Icon.vue';
import cuid from 'cuid'
import { commonEmojis } from '@/utils'
import { rxDBInstance as rxdb } from '@/rxdb/index'

const { currentUser } = useUserSession()

const props = defineProps<{
  open: boolean
  item: chrome.bookmarks.BookmarkTreeNode | undefined
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [item: object | null]
}>()

const itemSchema = toTypedSchema(
  z.object({
    title: z.string().min(1),
  }),
)
const form = useForm({
  validationSchema: itemSchema,
})

// init Extra
const existExtra = ref<{ note: string; emoji: string } | null>(null)
const editedExtra = ref({
  note: "",
  emoji: ""
})
async function resetExtra() {
  if (!props.item) return
  const dbExtra = await rxdb.extras.findOne({
    selector: {
      bookmarkId: props.item.id
    }
  }).exec();

  if (existExtra.value) {
    editedExtra.value = {
      note: existExtra.value.note,
      emoji: existExtra.value.emoji
    }
  } else {
    editedExtra.value = {
      note: "",
      emoji: ""
    }
  } 
}

// init Tags
const allTags = ref<string[]>([])
const searchTag = ref('')
const existTags = ref<string[]>([])
const selectedTags = ref<string[]>([])
const isPopoverOpen = ref(false)
const isEmojiPopoverOpen = ref(false)
async function resetTags() {
  if (!props.item) return 
  // init existTags
  const dbTags = await rxdb.tags.find({}).exec();
  allTags.value = dbTags.map((doc: any) => doc.name).sort((a, b) => a.localeCompare(b));
  // set existTags
  const bookmarkTags = await rxdb.tags.find({
    selector: {
      bookmarkIds: {
        $in: [props.item.id]
      },
    }
  }).exec();
  existTags.value = bookmarkTags.map((doc: any) => doc.name);
  selectedTags.value = [...existTags.value];
}

const onSubmit = form.handleSubmit(async (values) => {
  if (!props.item) {
    return
  }
  const browserAccount = await browser.identity.getProfileUserInfo()
  try {
    const updatedItem = await browser.bookmarks.update(props.item.id, { title: values.title })
    // 处理 extra 数据，如果已有 extra 那么直接更新，如果没有，检查是否有数据，有数据则创建 extra
    const dbExtra = await rxdb.extras.findOne({
      selector: {
        bookmarkId: props.item!.id,
        browserAccountId: browserAccount.id,
        userId: currentUser.value?.id
      }
    }).exec();
    if (dbExtra) {
      existExtra.value = await dbExtra.patch({
        note: editedExtra.value.note.trim(),
        emoji: editedExtra.value.emoji,
        updatedAt: new Date().toISOString(),
      });
    } else {
      if (editedExtra.value.note.trim() !== "" || editedExtra.value.emoji) {
        existExtra.value = await rxdb.extras.insert({
          id: cuid(),
          bookmarkId: props.item?.id,
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          note: editedExtra.value.note.trim(),
          emoji: editedExtra.value.emoji,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
      }
    }
    // 处理 tags 数据
    selectedTags.value.map(async (tag) => {
      const existTag = await rxdb.tags.findOne({
        selector: {
          browserAccountId: browserAccount.id,
          userId: currentUser.value?.id,
          name: tag
        }
      }).exec();
      if (existTag) {
        await existTag.patch({
          bookmarkIds: Array.from(new Set([...existTag.bookmarkIds, props.item?.id])), // set the age of every found to 12
          updatedAt: new Date().toISOString(),
        });
      } else {
        await rxdb.tags.insert({
          id: cuid(),
          userId: currentUser.value?.id,
          browserAccountId: browserAccount.id,
          name: tag,
          bookmarkIds: [props.item?.id],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
      }
    })
    emit('saved', updatedItem)
    emit('openChange', false)
    toast.success(i18n.t('form.result.saved'))
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(i18n.t('error.unknown'))
  }
})

// 添加监听器，当对话框打开时获取数据
watch(() => props.open, async (isOpen) => {
  if (isOpen && props.item) {   
    // 如果父组件没有传入item，则主动获取
    resetExtra()
    resetTags()
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle><Icon name="lucide:folder" /></DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <form
        id="editForm"
        class="mt-2 flex flex-col items-stretch gap-4"
        :validation-schema="itemSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
          :value="item?.title" 
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <Popover v-model:open="isPopoverOpen" class="min-w-(--reka-select-trigger-width) w-(--reka-select-trigger-width)">
          <PopoverTrigger as-child>
            <TagsInput
              v-model="selectedTags"
              @focus="isPopoverOpen = true"
              @blur="isPopoverOpen = false"
              class="w-full"
            >
              <TagsInputItem v-for="tag in selectedTags" :key="tag" :value="tag">
                <TagsInputItemText />
                <TagsInputItemDelete />
              </TagsInputItem>
              <TagsInputInput :placeholder="i18n.t('form.tags.placeholder')" />
            </TagsInput>
          </PopoverTrigger>

          <PopoverContent @open-auto-focus.prevent class="p-0 w-(--reka-popover-trigger-width)" @pointerdownOutside="isPopoverOpen = false"  >
            <Command v-model="selectedTags" v-model:search-tag="searchTag" multiple>
              <CommandInput placeholder="Search..." :auto-focus="false" />
              <CommandList>
                <CommandEmpty>{{ i18n.t('form.tags.noResults') }}</CommandEmpty>
                <CommandGroup class="flex flex-wrap gap-2 p-2">
                  <CommandItem as="span"
                    v-for="tag in allTags"
                    :key="tag"
                    :value="tag"
                    class="bg-muted rounded px-2 py-1 data-[highlighted]:bg-primary data-[highlighted]:text-primary-foreground data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                    @select.prevent="(ev: any) => {
                      if (typeof ev.detail.value === 'string') {
                        if (selectedTags.includes(ev.detail.value)) {
                          const index = selectedTags.indexOf(ev.detail.value)
                          selectedTags.splice(index, 1)
                        } else {
                          selectedTags.push(ev.detail.value)
                        }
                      }
                    }"
                  >
                    {{ tag }}
                  </CommandItem>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <FormField
          v-slot="{ componentField }"
          name="note"
          v-model="editedExtra.note"
        >
          <FormItem>
            <FormControl>
              <Textarea
                class="bg-background"
                :placeholder="i18n.t('form.note.placeholder')"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        
        <div id="emoji" class="flex items-center">
          <Popover v-model:open="isEmojiPopoverOpen">
            <PopoverTrigger as-child>
              <Button 
                variant="outline" 
                class="justify-center"
                type="button"
              >
                <span v-if="editedExtra.emoji" class="text-base">{{ editedExtra.emoji }}</span>
                <Icon v-else name="radix-icons:face" class="w-4 h-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent 
              class="w-[300px] p-0" 
              @open-auto-focus.prevent
            >
              <div class="flex flex-wrap p-2 gap-2">
                <Input
                  :placeholder="i18n.t('form.emoji.placeholder')"
                  v-model="editedExtra.emoji"
                  ref="emojiInput"
                />
                <Button
                  v-for="emoji in commonEmojis"
                  :key="emoji"
                  variant="ghost"
                  size="sm"
                  class="text-xl h-10 w-10 p-0 rounded-full hover:bg-accent"
                  @click.stop="editedExtra.emoji = emoji; isEmojiPopoverOpen = false"
                >
                  {{ emoji }}
                </Button>
              </div>
              <!-- <a href="https://getemoji.com/" target="_blank" class="text-sm text-muted-foreground">getemoji.com</a> -->
            </PopoverContent>
          </Popover>
        </div>
      </form>

      <DialogFooter class="mt-2">
        <Button
          type="submit"
          form="editForm"
        >
          {{ i18n.t('dialog.action.save') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
