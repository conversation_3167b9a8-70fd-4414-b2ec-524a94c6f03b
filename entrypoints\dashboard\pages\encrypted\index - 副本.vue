<script setup lang="ts">
import type { RxDatabase } from 'rxdb'
import { useRout<PERSON>, useRouter } from 'vue-router';
import { format as dateFormat } from 'date-fns'
import ViewOptions from '../../components/ViewOptions.vue'
import FolderEditDialog from '../../components/FolderEditDialog.vue'
import BookmarkEditDialog from '../../components/BookmarkEditDialog.vue'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { refDebounced } from '@vueuse/core'
import Icon from '@/components/Icon.vue'
import { onClickOutside } from '@vueuse/core'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from '@/components/ui/context-menu'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import ListItem from '../../components/ListItem.vue'
import Breadcrumbs from '../../components/Breadcrumbs.vue'
import { Badge } from '@/components/ui/badge'
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import SortingOptions from '../../components/SortingOptions.vue'
import { useDialog } from '@/composables/useDialog';
import { computedAsync } from '@vueuse/core'
import { useCrypto } from '@/composables/useCrypto';
import { rxDBInstance as rxdb } from '@/rxdb/index'
import { Subscription } from 'rxjs'
import { countBookmarksAndFoldersById, getFlattenedSubTree, sortBookmarks } from '@/utils/bookmark'
const { openDialog } = useDialog();

const crypto = useCrypto();



const { currentUser } = useUserSession();


const route = useRoute();
const router = useRouter();

// 首先声明所有响应式变量
const totalBookmarksCount = ref<number>(0);
const search = ref<string>('')
const searchDebounced = refDebounced(search, 35)
const includeSubfolders = ref(true)

// const props = defineProps<{
//   currentFolderId: string
// }>()

const currentFolderId = ref('1')  // 初始值直接使用 route.query.id
watchEffect(async () => {
  if (route.query.id && route.query.id !== currentFolderId.value) {
    currentFolderId.value = route.query.id as string;
  }
});

// 注入 setSelectedFolderId 方法
const setSelectedFolderId = inject<(folderId: string) => void>('setSelectedFolderId')

const flatBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])

// select
import { select, selectAll } from '@/utils/multiselect'
const pivotId = ref<string | null>(null)
const selectedIds = ref<string[]>([])
const ids = ref<string[]>([])

const selectedItem = computed(() => {
  return flatBookmarks.value.find(item => item.id === selectedIds.value[0])
})
const editBookmarkDialogOpen = ref(false)
const editFolderDialogOpen = ref(false)
const editTagDialogOpen = ref(false)
const currentTag = ref<{ id: string; name: string } | null>(null)
const bulkAddTagDialogOpen = ref(false)
const bulkRemoveTagDialogOpen = ref(false)
const bulkNoteDialogOpen = ref(false)
const bulkEmojiDialogOpen = ref(false)

function handleClick(itemId: string, e?: PointerEvent) {
  if (e?.ctrlKey || e?.metaKey) {
    const [pivot, newIds] = select({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      id: itemId
    })
    pivotId.value = pivot
    selectedIds.value = newIds
  } else if (e?.shiftKey) {
    const selected = selectAll({
      selectedIds: selectedIds.value,
      selectedId: pivotId.value!,
      ids: ids.value!,
      id: itemId
    })
    selectedIds.value = selected
  } else {
    if (selectedIds.value.includes(itemId) && selectedIds.value.length === 1) {
      pivotId.value = null
      selectedIds.value = []
    } else {
      pivotId.value = itemId
      selectedIds.value = [itemId]
    }
  }
}

function openBookmark(item: chrome.bookmarks.BookmarkTreeNode) {
  // 在新标签页中打开书签
  if (item.url) {
    browser.tabs.create({ url: item.url })
    selectedIds.value = [item.id]
  } else {
    setSelectedFolderId?.(item.id)
  }
}

// 实现类似 chrome 书签管理器的批量打开书签功能，循环打开 selectedIds 中的书签
// type 分别为普通打开，在新窗口中打开，以及在无痕式窗口中打开
function bulkOpenBookmark(type: 'tab' | 'window' | 'incognito') {
  const urls = selectedIds.value
    .map(id => flatBookmarks.value.find(item => item.id === id)?.url)
    .filter(Boolean) as string[];
  if (urls.length === 0) return;
  if (type === 'tab') {
    urls.forEach(url => browser.tabs.create({ url }));
  } else {
    // 在新窗口/无痕窗口一次性打开所有标签页
    browser.windows.create({
      url: urls,
      incognito: type === 'incognito',
      state: 'maximized'
    });
  }
}

// 添加专门的右键处理函数
function handleRightClick(e: MouseEvent, itemId: string) {  
  // 如果当前右键项不在选中列表中
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
    pivotId.value = itemId
  }
}

function openEditTagDialog(tag: { id: string; name: string }) {
  currentTag.value = tag
  editTagDialogOpen.value = true
}

function handleEdit(id?: string) {
  if (id) {
    selectedIds.value = [id]
  }
  if (selectedItem?.value?.url) {
    editBookmarkDialogOpen.value = true
  } else {
    editFolderDialogOpen.value = true
  }
}

function isSelected(id: string) {
  return selectedIds.value.includes(id)
}

const multiSelectTarget = ref<HTMLElement | null>(null)
const contextMenuOpen = ref(false);
onClickOutside(multiSelectTarget, () => {
  if (
    !contextMenuOpen.value && 
    !editFolderDialogOpen.value && 
    !editBookmarkDialogOpen.value && 
    !confirmDeleteDialog.value &&
    !bulkAddTagDialogOpen.value &&
    !bulkRemoveTagDialogOpen.value &&
    !bulkNoteDialogOpen.value &&
    !bulkEmojiDialogOpen.value
  ) {
    selectedIds.value = []
    pivotId.value = null
  }
}, {ignore: []});

function itemDragStart(itemId: string) {
  if (!selectedIds.value.includes(itemId)) {
    selectedIds.value = [itemId]
  }
}

// 通用的分批渲染函数
const animationFrameId = ref<number | null>(null);
async function renderInBatches(items: chrome.bookmarks.BookmarkTreeNode[]) {
  const start = performance.now();
  // 取消之前的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }

  const sorted = sortBookmarks(items, sortBy.value)
  
  ids.value = sorted.map(x => x.id);
  let index = 0;
  const batchSize = 15;
  let currentBatch: chrome.bookmarks.BookmarkTreeNode[] = [];
  // 清空当前列表 - 会导致闪烁
  flatBookmarks.value = [];

  function processBatch() {
    const end = Math.min(index + batchSize, sorted.length);
    for (; index < end; index++) {
      currentBatch.push(sorted[index]);
    }
    
    flatBookmarks.value = [...currentBatch];
    
    if (index < sorted.length) {
      animationFrameId.value = requestAnimationFrame(processBatch);
    } else {
      const end = performance.now();
      console.log(`All items processed took ${(end - start).toFixed(0)} milliseconds`);
      animationFrameId.value = null;
    }
  }

  processBatch();
}

// 在props定义后添加当前文件夹的ref
const currentFolder = ref<chrome.bookmarks.BookmarkTreeNode>();
// 在现有响应式变量后添加排序相关状态
const sortBy = ref<{ field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }>({
  field: 'default',
  direction: 'asc'
});

// 修改setBookmarks函数，添加排序逻辑
async function setBookmarks() {
  const start = performance.now();
  
  const bookmarks = await rxdb.bookmarks.find({
    selector: {
      folderId: currentFolderId.value
    }
  }).exec();
  
  renderInBatches(bookmarks as unknown as chrome.bookmarks.BookmarkTreeNode[]);
  const end = performance.now();
  console.log(`数据加载耗时 ${(end - start).toFixed(0)} 毫秒`);
}

// 修改排序方法参数类型
function handleSort(field: 'default' | 'title' | 'dateAdded' | 'url' | 'id') {
  if (sortBy.value.field === field) {
    // 切换排序方向
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc';
  } else {
    // 切换排序字段并重置方向
    sortBy.value.field = field;
    sortBy.value.direction = 'asc';
  }
  renderInBatches(flatBookmarks.value); // 重新加载数据应用排序
}

// 在 watch 监听器中添加取消逻辑
watch(() => currentFolderId.value, async (newVal) => {  
  console.warn('--------- currentFolderId.value -----------', newVal)
  // 取消正在进行的动画帧
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }
  
  // 保持原有重置逻辑...
  ids.value = [];
  search.value = '';
  selectedIds.value = [];
  pivotId.value = null;
  
  // 顺序执行关键操作
  setBookmarks();
}, { immediate: true });

watch([searchDebounced], async () => {
  await searchItems()
}, { immediate: false});

watch(() => includeSubfolders.value, async () => {
  await searchItems()
}, { immediate: false});

// 搜索
const isFiltered = computed(() => search.value.length > 0)
async function searchItems() {
  console.warn('--------- 触发搜索 -----------')
  const allstart = performance.now();
  const currentLevelBookmarks = await browser.bookmarks.getChildren(currentFolderId.value);
  
  if (!searchDebounced.value) {
    renderInBatches(currentLevelBookmarks);
    console.warn('--------- 没有搜索词也没有选中 Tag -----------')
    return
  }

  // 是否搜索子文件夹
  let filtered = includeSubfolders.value
    ? await getFlattenedSubTree(currentFolderId.value)
    : currentLevelBookmarks;
  
  // 关键词搜索
  if (searchDebounced.value) {
    const start3 = performance.now();
    // 搜索 title 和 url ，并返回匹配关键词的 ids
    const lowerTerm = searchDebounced.value.toLowerCase();
    // 搜索 bookmarks 中的 title 和 url 字段, 并返回匹配的 ids 合集
    const keywordFilteredIds = filtered
      .filter(item => {
        const title = item.title?.toLowerCase() || '';
        const url = item.url?.toLowerCase() || '';
        return title.includes(lowerTerm) || url.includes(lowerTerm);
      })
      .map(item => item.id);
    // 合并 keywordFilteredIds 和 extraBookmarkIds 并去重
    const combinedIds = [...new Set([...keywordFilteredIds])];
    filtered = filtered.filter(item => combinedIds.includes(item.id));
    const end3 = performance.now();
    console.log(`search keyword took ${(end3 - start3).toFixed(0)} milliseconds`)
  }
  
  totalBookmarksCount.value = filtered.length
  // 分批渲染，最多渲染 100 个结果
  renderInBatches(filtered.slice(0, 100));
  const allend = performance.now();
  console.log(`搜索结果 took ${(allend - allstart).toFixed(0)} milliseconds`)
}

function resetSearch() {
  search.value = ''
}

// 删除处理函数
const confirmDeleteDialog = ref(false)
const performDelete = async () => {
  try {
    // 获取所有选中项目的详细信息
    const items = await Promise.all(
      selectedIds.value.map(id => 
        browser.bookmarks.get(id).then(results => results[0])
      )
    );

    // 根据类型执行不同的删除操作
    await Promise.all(
      items.map(item => {
        if (item.url) { // 书签节点
          return browser.bookmarks.remove(item.id);
        } else { // 文件夹节点
          return browser.bookmarks.removeTree(item.id);
        }
      })
    );

    selectedIds.value = [];
    confirmDeleteDialog.value = false;
  } catch (error) {
    console.error('删除失败:', error);
  }
}

function handleNewFolderClick() {
  if (!currentUser.value) {
    openDialog('needToLogin')
    return;
  }

  if (crypto.isLocked.value) {
    openDialog('unlockEncrypt')
    return;
  }

  openDialog('newEncryptedFolder')
}

const encryptedBookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
const bookmarks = computedAsync(async () => {
  if (crypto.isLocked.value) return encryptedBookmarks.value;

  // 如果存在，解密后返回
  const decryptedBookmarks = [];
  for (const bookmark of encryptedBookmarks.value) {
    const decrypted = await crypto.decryptBookmark(bookmark);
    if (decrypted) decryptedBookmarks.push(decrypted);
  }
  return decryptedBookmarks;
}, []); // 初始值为空数组

// 订阅实时查询
const unsubscribe = ref<Subscription | null>(null);
onMounted(async () => {
  // 订阅 bookmarks 查询, 使用 key 来在 background 取消旧订阅, 查询条件可以根据需求调整
  const key = 'encryptedFolderTree:subscriptionId';
  unsubscribe.value = rxdb.bookmarks.find({
    selector: {
      category: 'encrypted',
      parentId: null,
    }
  }).$.subscribe((results) => {
    console.warn('liveQueryUpdate - sended!', results)
    encryptedBookmarks.value = results;
  });
});

onUnmounted(() => {
  // 清理订阅
  if (unsubscribe.value) {
    unsubscribe.value.unsubscribe();
  }
});
</script>

<template>
  <div>
    <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
      Encrypted Bookmarks {{ crypto.isLocked }}
    </h1>
    <Button
      variant="ghost"
      size="icon"
      class="h-6 w-6"
      @click="handleNewFolderClick()"
    >
      <Icon
        name="lucide:plus"
        class="size-4"
      />
    </Button>
    <div class="flex justify-between mt-4 items-center">
      <div class="flex flex-col space-y-1 items-start">
        <Breadcrumbs :folderId="currentFolderId"></Breadcrumbs>
      </div>
    </div>
    
    <div class="flex items-center justify-between mt-4">
      <div class="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter bookmarks ..."
          v-model="search"
          class="h-10 w-[220px] lg:w-[350px] rounded-full pl-4 bg-secondary pt-0 focus-visible:ring-2"
        />
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="includeSubfolders" 
            :model-value="includeSubfolders"
            class="h-4 w-4"
          />
          <label 
            for="includeSubfolders" 
            class="text-sm font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Sub folders
          </label>
        </div>
        <Button
          v-if="isFiltered"
          variant="ghost"
          class="h-8 px-2 lg:px-3"
          @click="resetSearch()"
        >
          Reset
          <Icon name="radix-icons:cross-2" />
        </Button>
      </div>
      <div class="flex items-center gap-2">
        <SortingOptions 
          :sort-by="sortBy"
          @sort="handleSort"
        />
        <ViewOptions />
      </div>
    </div>

    <div class="mt-4">
      <h3 
        v-if="isFiltered" 
        class="text-sm text-muted-foreground"
      >
        {{ i18n.t('options.list.searchResults', [totalBookmarksCount, flatBookmarks.length, 100]) }}
      </h3>
    </div>
    <div v-if="bookmarks.length" class="rounded-md border mt-4 py-2" ref="multiSelectTarget">
      <ContextMenu 
          v-model:open="contextMenuOpen"
          @open-change="(open: boolean) => contextMenuOpen = open"
        >
          <ContextMenuTrigger>
          <ul>
            <ListItem
              v-for="item in bookmarks"
              :key="item.id"
              :data-state="isSelected(item.id) && 'selected'"
              @contextmenu="(e: MouseEvent) => handleRightClick(e, item.id)"
              @click.stop="(e: PointerEvent) => handleClick(item.id, e)"
              @dblclick="openBookmark(item)"
              class="transition-none data-[state=selected]:bg-muted"
              :item="item"
              :selectedIds="selectedIds"
              :tags="[]"
              :extra="{note: '', emoji: ''}"
              @drag-start="itemDragStart"
              @open-context-menu="contextMenuOpen = true"
              @delete="(id) => { handleClick(id); confirmDeleteDialog = true }"
              @edit="(id) => handleEdit(id)"
            >
            </ListItem>
          </ul>
          </ContextMenuTrigger>
          <ContextMenuContent 
            class="v-context-menu-content"
          >
            <ContextMenuItem 
              :disabled="selectedIds.length > 1"
              @select="handleEdit()"
            >
              {{ i18n.t('context_menu.edit') }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="confirmDeleteDialog = true"
            >
              {{ i18n.t('context_menu.deleteWithCount', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuSub>
              <ContextMenuSubTrigger>
                {{ i18n.t('context_menu.bulkActions') }}
              </ContextMenuSubTrigger>
              <ContextMenuSubContent>
                <ContextMenuItem 
                  @select="bulkAddTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkAddTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkRemoveTagDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkRemoveTag') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkNoteDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkNote') }}
                </ContextMenuItem>
                <ContextMenuItem 
                  @select="bulkEmojiDialogOpen = true"
                >
                  {{ i18n.t('context_menu.bulkEmoji') }}
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
            <ContextMenuSeparator />
            <ContextMenuItem 
              @select="bulkOpenBookmark('tab')"
            >
              {{ i18n.t('context_menu.openAll', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('window')"
            >
              {{ i18n.t('context_menu.openInNewWindow', [selectedIds.length]) }}
            </ContextMenuItem>
            <ContextMenuItem 
              @select="bulkOpenBookmark('incognito')"
            >
              {{ i18n.t('context_menu.openIncognito', [selectedIds.length]) }}
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
    </div>
    <div v-else class="flex flex-col h-48 text-center rounded-md border mt-4 justify-center gap-2">
      <h3 class="text-lg font-semibold">
        {{ i18n.t('options.list.noResults') }}
      </h3>
      <p class="text-sm text-muted-foreground">
        {{ i18n.t('options.list.noResultsDescription') }}
      </p>
    </div>

    <FolderEditDialog
      :open="editFolderDialogOpen"
      :item="selectedItem"
      @open-change="(open: boolean) => (editFolderDialogOpen = open)"
      @saved=""
    />

    <BookmarkEditDialog
      :open="editBookmarkDialogOpen"
      :item="selectedItem"
      @open-change="(open: boolean) => (editBookmarkDialogOpen = open)"
      @saved=""
    />

    <AlertDialog v-model:open="confirmDeleteDialog">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Deletion of {{ selectedIds.length }} Items</AlertDialogTitle>
          <AlertDialogDescription>
            This action will permanently delete the selected bookmarks/folders ({{ selectedIds.length }} items). This operation cannot be undone!
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant="outline" @click="confirmDeleteDialog = false">
            Cancel
          </Button>
          <Button variant="destructive" @click="performDelete()">
            Confirm Delete
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>
