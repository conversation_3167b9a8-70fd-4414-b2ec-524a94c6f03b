<script lang="ts" setup>
import { useColorMode } from '@vueuse/core'
import { TooltipProvider } from  '@/components/ui/tooltip'
import Icon from '@/components/Icon.vue';
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { provide } from 'vue'
import { initRxDB } from '@/rxdb/index'

import Edit from './Edit.vue'
import UserMenu from './components/UserMenu.vue'
import { Toaster } from '@/components/ui/sonner'
import { openOrActivateManage } from '@/utils/bookmark'

interface TabState {
  id: string
  existBookmark: chrome.bookmarks.BookmarkTreeNode | null
  folderName: string
}

const colorMode = useColorMode({
  storageKey: 'side-panel-vueuse-color-scheme',  // 显式指定存储键名
});
// 从 chrome.storage.sync 中获取 sidePanelcolorMode
browser.storage.sync.get(['side-panel-color-mode'], (result) => { 
  colorMode.value = result['side-panel-color-mode'] || 'light'; // 默认值为 'dark'
});
// 监听 chrome.storage.sync 中 colorMode 的变化
browser.storage.onChanged.addListener((changes) => {
  if (changes['side-panel-color-mode']) {  // 修改键名
    colorMode.value = changes['side-panel-color-mode'].newValue; 
  }
});
const toggleColorMode = () => {
  const newColorMode = colorMode.value === 'light' ? 'dark' : 'light';
  browser.storage.sync.set({ 'side-panel-color-mode': newColorMode }); // 修改键名
};

const browserTab = ref<chrome.tabs.Tab | null>(null)
const editTabs = ref<TabState[]>([])
const activeTab = ref('')
async function initTabs() {
  console.warn('--------- initTabs ---------', browserTab.value?.url)
  const browserTabs = await browser.tabs.query({active: true, lastFocusedWindow: true});
  browserTab.value = browserTabs[0]
  
  // 清空现有tabs数组
  editTabs.value = []
  
  const existBookmarks = await getBrowserBookmarksByUrl();
  
  // 逐个处理已存在的书签
  for (const bm of existBookmarks) {
    const folderName = await getFolderName(bm.parentId!)
    editTabs.value.push({
      id: `bm-${bm.id}`,
      existBookmark: bm,
      folderName
    })
  }
  
  // 当没有书签时
  if (editTabs.value.length === 0) {
    addNewTab()
  }
  
  activeTab.value = editTabs.value[0]?.id || '';
}

function addNewTab() {
  const newTab = {
    id: `tab-${Date.now()}-${Math.floor(Math.random()*1000)}`, // 添加随机数
    existBookmark: null,
    folderName: 'blank'
  };
  
  editTabs.value.push(newTab);
  activeTab.value = newTab.id;
}

function removeTab(tabId: string) {
  if (editTabs.value.length <= 1) return
  editTabs.value = editTabs.value.filter(t => t.id !== tabId)
  if (activeTab.value === tabId) {
    activeTab.value = editTabs.value[0]?.id || ''
  }
}

// 获取书签逻辑
async function getBrowserBookmarksByUrl() {
  if (!browserTab.value?.url) return []
  
  // 使用精确匹配查询
  const browserBookmarks = await browser.bookmarks.search({
    url: browserTab.value.url
  })
  return browserBookmarks // 精确匹配
}

// 打开书签管理器
function handleOpenManage() {
  const existBookmarkTab = editTabs.value.find(t => t.existBookmark);
  if (existBookmarkTab?.existBookmark) {
    openOrActivateManage(existBookmarkTab.existBookmark.parentId, existBookmarkTab.existBookmark.id);
  } else {
    openOrActivateManage();
  }
}

async function test() {
  const cookie = await chrome.cookies.get({ name: "nuxt-session", url: "http://localhost:3000" });
  console.log(cookie);
}

// 消息处理函数/监听书签变化
function handleRuntimeMessage(
  request: any
) {
  console.warn('--------- popup App 收到消息 -----------!', request);
  
  // 处理书签相关消息
  if (request.type === 'bookmark-change') {
    // 只有当书签的URL与当前标签页URL匹配时才重新加载
    // initTabs();
    // if (url === browserTab.value?.url) {
    //   console.log('相关书签变化，重新加载数据');
    //   initTabs();
    // }
  }

  if (request.type === 'closeSidePanel') {
    window.close();
  }
}

// 监听标签页更新事件，切换标签页不会触发
browser.tabs.onUpdated.addListener(async function(tabId, changeInfo, tab) {
  // changeInfo 是每个属性变化了都会触发一次，比如 url, faviconUrl, 并且只传变化了的那个属性，f5 时 changeInfo.url 不会触发！
  console.warn('--------- 标签页 onUpdated ---------', tabId, changeInfo, changeInfo.url, tab.url);
  if (changeInfo.url || tab.url !== browserTab.value?.url) {
    // 如果这是当前激活的标签页
    const [activeTab] = await browser.tabs.query({active: true, currentWindow: true});
    if (activeTab.id === tabId) {
      await initTabs();
    }
  }
});

// 监听标签页激活事件，切换标签页时会触发
browser.tabs.onActivated.addListener(async function(activeInfo) {
  console.warn('---------tabs.onActivated---------');
  // 获取新激活的标签页信息
  console.warn('--------- 标签页 onActivated ---------', activeInfo);
  const tab = await browser.tabs.get(activeInfo.tabId);
  if (tab.url !== browserTab.value?.url) {
    await initTabs();
  }
});

// 在父组件中处理 remove 事件
function handleRemoveTab(tabId: string) {
  editTabs.value = editTabs.value.filter(tab => tab.id !== tabId);
  if (activeTab.value === tabId) {
    // 如果删除的是当前激活的标签页，则激活第一个标签页
    activeTab.value = editTabs.value[0]?.id || '';
  }
  // 如果当前已无标签页，那么新建一个标签页 addNewTab()
  if (editTabs.value.length === 0) {
    addNewTab()
  }
}

// 在父组件中处理 updateExistBookmark 事件
function handleUpdateExistBookmark(bookmark: chrome.bookmarks.BookmarkTreeNode) {
  const tab = editTabs.value.find(t => t.id === activeTab.value);
  if (tab) {
    tab.existBookmark = bookmark;
  }
}

async function getFolderName(folderId: string) {
  const folders = await browser.bookmarks.get(folderId)
  return folders[0]?.title
}

const getFolderNameAsync = async (parentId: string) => {
  return await getFolderName(parentId)
}

onMounted(async () => {
  await initRxDB();
  // 监听 background 的消息
  browser.runtime.onMessage.addListener(handleRuntimeMessage);
  console.warn('--------- onMounted initTabs() ---------')
  await initTabs();
  analytics.setEnabled(true);
  // 加载存储的配置
})

onBeforeUnmount(() => {
  browser.runtime.onMessage.removeListener(handleRuntimeMessage);
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <Toaster :duration="1000" />
    <div id="sidepanel-container" class="bg-background w-[520px]">
      <div id="sidepanel-header" class="flex justify-between bg-secondary p-1">
        <UserMenu />
        <div class="flex items-center text-muted-foreground">
          <Button size="icon" variant="ghost" @click="handleOpenManage">
            <Icon name="lucide:home"></Icon>
          </Button>
          <Button size="icon" variant="ghost" @click="toggleColorMode">
            <Icon :name="colorMode === 'light' ? 'lucide:moon' : 'lucide:sun'" />
          </Button>

          <!-- <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button
                aria-haspopup="true"
                size="sm"
                variant="ghost"
              >
                <Icon name="radix-icons:dots-horizontal" />
                <span class="sr-only">Toggle menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="toggleColorMode">
                Light / Dark
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> -->
        </div>
      </div>
      <div id="sidepanel-content" class="">
        <Tabs v-model:modelValue="activeTab" class="gap-0">
          <div class="flex items-center overflow-x-auto whitespace-nowrap bg-muted px-2 pt-0">
            <TabsList class="inline-flex flex-nowrap rounded-none p-0">
              <TabsTrigger
                v-for="(tab, index) in editTabs"
                :key="tab.id"
                :value="tab.id"
                class="relative shrink-0 rounded-none rounded-t-sm shadow-none"
                :class="{ 'cursor-default': activeTab === tab.id }"
              >
                <div class="flex items-center gap-1 py-1">
                  <Icon 
                    :name="tab.existBookmark?.id ? 'radix-icons:star-filled' : 'radix-icons:star'" 
                    class="h-5 w-5 text-amber-400"
                  />
                  <span class="text-sm"
                    :class="{ 'text-muted-foreground italic font-normal': !tab.existBookmark }"
                  >{{ tab.folderName }}</span>
                  <span class="text-sm cursor-pointer"
                    v-if="editTabs.length > 1 && !tab.existBookmark"
                    variant="ghost"
                    size="sm"
                    @click.stop="removeTab(tab.id)"
                  >
                    <Icon name="radix-icons:cross-2"  />
                  </span>
                </div>
              </TabsTrigger>
            </TabsList>
            <Button
              size="icon"
              variant="ghost"
              @click="addNewTab"
              :disabled="!browserTab?.url"
            >
              <Icon name="radix-icons:plus" class="h-4 w-4" />
            </Button>
          </div>

          <div id="tab-content">
            <div 
              v-for="tab in editTabs"
              :key="tab.id"
              class="absolute top-0 left-0 w-full"
              :class="{ 'relative block': activeTab === tab.id, 'hidden': activeTab !== tab.id }"
            >
              <Edit 
                v-if="browserTab?.url"
                :id="tab.id"
                :browserTab="browserTab"
                :existBookmark="tab.existBookmark"
                @remove="handleRemoveTab"
                @updateExistBookmark="handleUpdateExistBookmark"
              />
            </div>
          </div>
        </Tabs>
      </div>
    </div>
  </TooltipProvider>
</template>
