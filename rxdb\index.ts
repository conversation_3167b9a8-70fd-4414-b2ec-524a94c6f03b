import { createRxDatabase, RxJsonSchema, addRxPlugin, RxDatabase } from 'rxdb';
import { getRxStorageDexie } from 'rxdb/plugins/storage-dexie';
import { RxDBUpdatePlugin } from 'rxdb/plugins/update';
import { RxDBMigrationSchemaPlugin } from 'rxdb/plugins/migration-schema';
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
import { RxDBJsonDumpPlugin } from 'rxdb/plugins/json-dump';
addRxPlugin(RxDBJsonDumpPlugin);
// import { RxDBDevModePlugin } from 'rxdb/plugins/dev-mode';

// addRxPlugin(RxDBDevModePlugin);
addRxPlugin(RxDBQueryBuilderPlugin);
addRxPlugin(RxDBMigrationSchemaPlugin);
addRxPlugin(RxDBUpdatePlugin);
import cuid from 'cuid';

const collectionVersion = 1;
const currentBrowser = import.meta.env.BROWSER;
// 默认值只能使用常量不能使用函数，否则会导致 DB6 错误（Schema 不一致），也因此，主键 id 字段不能设置默认值
export const extraSchema: RxJsonSchema<any> = {
  title: 'Bookmark extra schema',
  description: 'The extra info with the bookmark',
  version: collectionVersion,
  primaryKey: "id",
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 50
    },
    userId: {
      type: 'string',
    },
    source: {
      type: 'string', // 来源：chrome / edge / safari / firefox / web
    },
    browserAccountId: {
      type: 'string',
    },
    bookmarkId: {
      type: 'string',
    },
    bookmarkType: {
      type: 'string', // 类型："browser" / "encrypted"
    },
    cover: {
      type: 'string',
    },
    description: {
      type: 'string',
    },
    note: {
      type: 'string',
    },
    emoji: {
      type: 'string',
    },
    iv: {
      type: 'string',
    },
    updatedAt: {
      type: 'string',
      format: "date-time"
    },
    createdAt: {
      type: 'string',
      format: "date-time"
    },
    deletedAt: {
      type: 'string',
      format: "date-time",
      default: null
    },
    // 不需要在 rxdb 表创建 deleted 字段，因为已指定 deletedField: 'deleted'， rxdb 会自动添加，服务端数据库则需要创建 deleted 字段以保存删除状态
    // deleted: {
    //   type: 'boolean',
    // },
    replicationRevision: {
      type: 'string',
      minLength: 3
    }
  }
};

export const tagSchema: RxJsonSchema<any> = {
  title: 'Tag schema',
  description: 'The tags associated with the bookmark',
  version: collectionVersion,
  primaryKey: "id",
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 50,
    },
    userId: {
      type: 'string',
    },
    source: {
      type: 'string',
    },
    browserAccountId: {
      type: 'string',
    },
    name: {
      type: 'string',
    },
    bookmarkIds: {
      type: 'array',
      uniqueItems: true,
      items: {
        type: "string"
      }
    },
    color: {
      type: 'string',
    },
    updatedAt: {
      type: 'string',
    },
    createdAt: {
      type: 'string',
    },
    // 不需要在 rxdb 表创建 deleted 字段，因为已指定 deletedField: 'deleted'， rxdb 会自动添加，服务端数据库则需要创建 deleted 字段以保存删除状态
    // deleted: {
    //   type: 'boolean',
    // },
    replicationRevision: {
      type: 'string',
      minLength: 3
    }
  }
  // required: ['name', 'userId','bookmarkIds', 'updatedAt', 'createdAt'],
  // indexes: ['name', 'userId', 'updatedAt', 'createdAt'], // bookmarkIds 使用 $in 不能做索引，索引的话会 $in 无效
};

export const sharedSchema: RxJsonSchema<any> = {
  title: 'Shared schema',
  description: 'The shared bookmark',
  version: collectionVersion,
  primaryKey: "id",
  type: 'object',
  properties: {
    id: {
      type: 'string',
      maxLength: 50
    },
    slug: {
      type: 'string',
    },
    userId: {
      type: 'string',
    },
    source: {
      type: 'string', // 来源：chrome / edge / safari / firefox / web
      default: 'chrome'
    },
    browserAccountId: {
      type: 'string',
    },
    bookmarkId: {
      type: 'string',
    },
    bookmarkData: {
      type: 'string',
    },
    bookmarkDataHash: {
      type: 'string',
    },
    description: {
      type: 'string',
    },
    isPublic: {
      type: 'boolean',
    },
    inviteCode: {
      type: 'string',
    },
    viewsCount: {
      type: 'number',
    },
    updatedAt: {
      type: 'string',
    },
    createdAt: {
      type: 'string',
    },
    deletedAt: {
      type: 'string',
    },
    replicationRevision: {
      type: 'string',
      minLength: 3
    }
  },
};

export let rxDBInstance: RxDatabase;
export const dbName = 'gomark';
// 获取或者创建一个 rxdb 实例
export const getRxDBInstance = async () => {
  if (rxDBInstance) {
    return rxDBInstance;
  }
  return await initRxDB();
}
// 初始化 rxdb
export const initRxDB = async () => {
  console.warn('initRxDB called')
  if (rxDBInstance) {
    console.warn('-------- rxDBInstance already exists----------', rxDBInstance)
    return rxDBInstance;
  }
  
  try {
    rxDBInstance = await createRxDatabase({
      name: dbName,                   // <- name
      storage: getRxStorageDexie(),       // <- RxStorage

      /* Optional parameters: */
      // password: 'myPassword',             // <- password (optional)
      // multiInstance: true,                // <- multiInstance (optional, default: true)
      // eventReduce: true,                  // <- eventReduce (optional, default: false)
      cleanupPolicy: {},                   // <- custom cleanup policy (optional) 
    });
    
    // await rxDBInstance.addCollections({
    //   tags: {
    //     schema: tagSchema,
    //     migrationStrategies: {
    //       1: (oldDoc) => {
    //         console.log('Migrating tag:', oldDoc);
    //         return {
    //           ...oldDoc,
    //           source: 'chrome'
    //         };
    //       }
    //     },
    //   }
    // });

    // 添加集合
    const collections = await rxDBInstance.addCollections({
      extras: {
        schema: extraSchema,
        migrationStrategies: {
          1: function(oldDoc){
            return oldDoc;
          }
        }
      },
      tags: {
        schema: tagSchema,
        migrationStrategies: {
          1: function(oldDoc){
            return oldDoc;
          }
        },
      },
      shareds: {
        schema: sharedSchema,
        migrationStrategies: {
          1: function(oldDoc){
            return oldDoc;
          }
        },
      }
    });
    
    // 添加 preInsert 钩子以便设置默认值
    collections.extras.preInsert(async function(docData) {
      const { user } = useUserSession()
      const browserAccount = await browser.identity.getProfileUserInfo();
      
      docData.id = docData.id ?? cuid();
      docData.userId = docData.userId ?? user.value?.id ?? null;
      docData.source = docData.source ?? currentBrowser;
      docData.browserAccountId = browserAccount.id;
      docData.bookmarkType = docData.bookmarkType ?? "browser";
      docData.cover = docData.cover ?? null;
      docData.description = docData.description ?? null;
      docData.note = docData.note ?? null;
      docData.emoji = docData.emoji ?? null;
      docData.createdAt = docData.createdAt ?? new Date().toISOString();
      docData.updatedAt = docData.updatedAt ?? new Date().toISOString();
      return docData;
    }, false); // false 表示串行执行，true 表示并行执行

    collections.tags.preInsert(async function(docData) {
      const { user } = useUserSession()
      const browserAccount = await browser.identity.getProfileUserInfo();

      docData.id = docData.id ?? cuid();
      docData.userId = docData.userId ?? user.value?.id ?? null;
      docData.source = docData.source ?? currentBrowser;
      docData.browserAccountId = browserAccount.id;
      docData.color = docData.color ?? null;
      docData.createdAt = docData.createdAt ?? new Date().toISOString();
      docData.updatedAt = docData.updatedAt ?? new Date().toISOString();
      return docData;
    }, false);

    collections.shareds.preInsert(async function(docData) {
      const { user } = useUserSession()
      const browserAccount = await browser.identity.getProfileUserInfo();

      docData.id = docData.id ?? cuid();
      docData.userId = docData.userId ?? user.value?.id ?? null;
      docData.source = docData.source ?? currentBrowser;
      docData.browserAccountId = browserAccount.id;
      docData.createdAt = docData.createdAt ?? new Date().toISOString();
      docData.updatedAt = docData.updatedAt ?? new Date().toISOString();
      return docData;
    }, false);

    return rxDBInstance;
  } catch (error) {
    console.error('initRxDB error', error);
  }
};

/**
 * 将 tags 和 extras 集合中 userId 为空的所有记录更新为当前登录用户的 ID
 */
export async function updateNullUserIdsToCurrentUser(currentUserId: string) {
  if (!rxDBInstance) {
    console.error('RxDB instance is not initialized.');
    return;
  }

  if (!currentUserId) {
    console.error('No current user logged in.');
    return;
  }

  try {
    // Update tags collection
    const tagsToUpdate = await rxDBInstance.tags
      .find({
        selector: {
          userId: { $exists: false },
        },
      })
      .exec();

    if (tagsToUpdate.length > 0) {
      await Promise.all(
        tagsToUpdate.map(async (tag) => {
          await tag.update({
            $set: {
              userId: currentUserId,
              updatedAt: new Date().toISOString(),
            },
          });
        })
      );
      console.log(`Updated ${tagsToUpdate.length} tags.`);
    }

    // Update extras collection
    const extrasToUpdate = await rxDBInstance.extras
      .find({
        selector: {
          userId: { $exists: false },
        },
      })
      .exec();

    if (extrasToUpdate.length > 0) {
      await Promise.all(
        extrasToUpdate.map(async (extra) => {
          await extra.update({
            $set: {
              userId: currentUserId,
              updatedAt: new Date().toISOString(),
            },
          });
        })
      );
      console.log(`Updated ${extrasToUpdate.length} extras.`);
    }

    // Update shareds collection
    const sharedsToUpdate = await rxDBInstance.shareds
      .find({
        selector: {
          userId: { $exists: false },
        },
      })
      .exec();

    if (sharedsToUpdate.length > 0) {
      await Promise.all(
        sharedsToUpdate.map(async (shared) => {
          await shared.update({
            $set: {
              userId: currentUserId,
              updatedAt: new Date().toISOString(),
            },
          });
        })
      );
      console.log(`Updated ${sharedsToUpdate.length} shareds.`);
    }

    console.log('All null userId records updated successfully.');
  } catch (error) {
    console.error('Error updating null userId records:', error);
  }
}

export default initRxDB;
