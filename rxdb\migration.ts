import { rxDBInstance } from "@/rxdb";

// 检查数据库是否存在
function checkDatabaseExists(dbName: string): Promise<boolean> {
  return new Promise((resolve) => {
    const request = indexedDB.open(dbName);
    let exists = true;
    
    request.onupgradeneeded = () => {
      // 数据库不存在或版本低，会触发此事件
      exists = false;
      // 中止操作，不真正创建/更新数据库
      request.transaction?.abort();
    };
    
    request.onsuccess = () => {
      // 如果数据库存在，关闭连接
      if (exists) request.result.close();
      resolve(exists);
    };
    
    request.onerror = () => {
      // 出错时假设数据库不存在
      resolve(false);
    };
  });
}

// 导出数据库数据
async function exportDatabaseToJSON(dbName: string) {
  // 先检查数据库是否存在
  const dbExists = await checkDatabaseExists(dbName);
  if (!dbExists) {
    console.log(`数据库 ${dbName} 不存在，返回空数据`);
    return [];
  }

  const storeName = 'docs'; // 你要读取的 Store

  return new Promise<any[]>((resolve, reject) => {
    const request = indexedDB.open(dbName);
    
    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const getAllRequest = store.getAll();

      getAllRequest.onsuccess = () => {
        const data = getAllRequest.result;
        console.log(`成功读取 ${data.length} 条数据`);
        db.close();
        resolve(data);
      };
      getAllRequest.onerror = (e) => {
        db.close();
        reject(e);
      };
    };
    
    request.onerror = (e) => {
      reject(`无法打开数据库 ${dbName}: ${e}`);
    };
  });
}

async function importExtrasDatabase() {
  try {
    const extrasData = await exportDatabaseToJSON('rxdb-dexie-bookmarkplus--0--extras');
    console.log('导出扩展数据:', extrasData.length, '条记录', extrasData);
    
    if (extrasData.length === 0) {
      console.log('没有数据需要导入');
      return;
    }
    
    const cleanedData = extrasData.map((doc: any) => {
      const isDeleted = doc._deleted === '0' ? false : true;
      return {
        ...doc,
        _deleted: isDeleted,
      };
    });
    
    await rxDBInstance.extras.bulkInsert(cleanedData);
    console.log(`成功导入 ${cleanedData.length} 条记录`);
    
    // 4. (可选)删除旧数据库
    const deleteRequest = indexedDB.deleteDatabase('rxdb-dexie-bookmarkplus--0--extras');
    deleteRequest.onsuccess = () => console.log('旧数据库已删除');
  } catch (e) {
    console.error('导入失败:', e);
  }
}

async function importTagsDatabase() {
  try {
    const tagsData = await exportDatabaseToJSON('rxdb-dexie-bookmarkplus--0--tags');
    console.log('导出标签数据:', tagsData.length, '条记录', tagsData);
    
    if (tagsData.length === 0) {
      console.log('没有数据需要导入');
      return;
    }
    
    const cleanedData = tagsData.map((doc: any) => ({
      ...doc,
      _deleted: doc._deleted == '0' ? false : true,
    }));
    
    await rxDBInstance.tags.bulkInsert(cleanedData);
    console.log(`成功导入 ${cleanedData.length} 条记录`);
    
    // 4. (可选)删除旧数据库
    const deleteRequest = indexedDB.deleteDatabase('rxdb-dexie-bookmarkplus--0--tags');
    deleteRequest.onsuccess = () => console.log('旧数据库已删除');
  } catch (e) {
    console.error('导入失败:', e);
  }
}

export const migrateData = async () => {
  // 导入标签数据
  await importTagsDatabase();
  await importExtrasDatabase();
  
  console.log('数据迁移完成');
};