import {replicateRxCollection, RxReplicationState} from 'rxdb/plugins/replication';
import { rxDBInstance, dbName } from "@/rxdb";
import { Subject } from 'rxjs';
import { RxReplicationPullStreamItem } from 'rxdb';

const { user } = useUserSession();

export const retryTimes = {
  extras: 10 * 1000,
  tags: 10 * 1000,
  shareds: 10 * 1000,
};

interface RxDBExtraCheckpoint {
  updatedAt: string;
}

interface RxDBTagCheckpoint {
  updatedAt: string;
}

interface RxDBSharedCheckpoint {
  updatedAt: string;
}

const endpoint = import.meta.env.VITE_ENDPOINT;
let extraReplication: RxReplicationState<any, RxDBExtraCheckpoint> | undefined;
let tagReplication: RxReplicationState<any, RxDBTagCheckpoint> | undefined;
let sharedReplication: RxReplicationState<any, RxDBSharedCheckpoint> | undefined;
// 每一个同步似乎只能跟踪一个表
const extraReplicationIdentifier = `${dbName}-extra-api-replication`;
const tagReplicationIdentifier = `${dbName}-tag-api-replication`;
const sharedReplicationIdentifier = `${dbName}-shared-api-replication`;

export const extraPullStream$ = new Subject<RxReplicationPullStreamItem<any, RxDBExtraCheckpoint>>();
export const tagPullStream$ = new Subject<RxReplicationPullStreamItem<any, RxDBTagCheckpoint>>();
export const sharedPullStream$ = new Subject<RxReplicationPullStreamItem<any, RxDBSharedCheckpoint>>();


export async function runRxDBReplication() {
  try {
    await createExtraReplication().catch((error) => console.error('Run extra Replication error', error));
    await createTagReplication().catch((error) => console.error('Run tag Replication error', error));
    await createSharedReplication().catch((error) => console.error('Run shared Replication error', error));
    console.info('Run RxDBReplication done');
  } catch (error) {
    console.error('Run RxDBReplication error', error);
  }
}

export async function cancelRxDBReplication() {
  try {
    await extraReplication?.cancel().catch((error) => console.error('Cancel extra Replication error', error));
    await tagReplication?.cancel().catch((error) => console.error('Cancel tag Replication error', error));
    await sharedReplication?.cancel().catch((error) => console.error('Cancel shared Replication error', error));
    extraReplication = undefined;
    tagReplication = undefined;
    sharedReplication = undefined;
    console.info('Cancel RxDBReplication done');
  } catch (error) {
    console.error('Cancel RxDBReplication error', error);
  }
}

export async function deleteRxDBReplication(params?: { reloadApp?: boolean }) {
  const {reloadApp = true} = params || {};

  try {
    await extraReplication?.collection.remove();
    extraReplication = undefined;
    await tagReplication?.collection.remove();
    tagReplication = undefined;
    await sharedReplication?.collection.remove();
    sharedReplication = undefined;
  } catch (error) {
    console.error('deleteRxDB error', error);
  }
  if (reloadApp) {
    window.location.reload();
  }
}

async function createExtraReplication() {
  if (extraReplication) {
    return extraReplication;
  }
  console.warn('-------------- createExtraReplication 1 -------------')
  try {
    if (!rxDBInstance?.collections?.extras) {
      throw new Error('extras collection is not initialized');
    }
  } catch (e) {
    console.error('createExtraReplication error:', e);
    extraReplication = undefined;
  }
  try {
    extraReplication = replicateRxCollection<any, RxDBExtraCheckpoint>({
      collection: rxDBInstance.collections.extras,
      replicationIdentifier: extraReplicationIdentifier,
      live: true,
      retryTime: retryTimes.extras,
      waitForLeadership: false,
      autoStart: true,
      deletedField: 'deleted',
      pull: {
        batchSize: 20,
        async handler(checkpointOrNull, batchSize){
          try {
            const updatedAt = checkpointOrNull ? checkpointOrNull.updatedAt : 0;
            const response = await fetch(`${endpoint}/api/sync/extras?updatedAt=${updatedAt}&limit=${batchSize}`);
            const data = await response.json();
            console.warn('------------- extraReplication pull --------------', data);
            return {
              documents: data.documents,
              checkpoint: data.checkpoint
            };
          } catch (error) {
            console.error('pull.handler error:', error);
            throw error; // 重新抛出以便外部捕获
          }
        },
        stream$: extraPullStream$.asObservable()
      },
      push: {
        batchSize: 20,
        async handler(changeRows) {
          /**
           * Push the local documents to a remote REST server.
           */
          if (!user.value?.id) {
            return;
          }

          changeRows = changeRows.filter(row => row.newDocumentState.userId === user.value?.id);

          console.warn('------------- extraReplication push --------------', changeRows)
          const rawResponse = await fetch(`${endpoint}/api/sync/extras`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ changeRows })
          });
          /**
           * Contains an array with all conflicts that appeared during this push.
           * If there were no conflicts, return an empty array.
           */
          // 默认的冲突处理程序将始终丢弃 fork 状态并使用 master 状态。这可确保长时间脱机的客户端在再次联机时不会意外覆盖其他人的更改。
          // 您可以通过在调用 addCollection() 时设置属性 conflictHandler 来指定自定义冲突处理程序。
          const conflictsArray = await rawResponse.json();
          console.warn("----------- conflictsArray ----------", conflictsArray)
          return conflictsArray;
        },
      },
    });
    console.warn('-------------- createExtraReplication 4 -------------')
  } catch (e) {
    console.warn('-------------- createExtraReplication 5 error -------------')
    extraReplication = undefined;
    console.error(e);
  }
  return extraReplication;
}

async function createTagReplication() {
  if (tagReplication) {
    return tagReplication;
  }
  console.warn('-------------- createTagReplication -------------')
  try {
    if (!rxDBInstance?.collections?.tags) {
      console.log('createTagReplication: collection not found');
      return;
    }
    tagReplication = replicateRxCollection<any, RxDBTagCheckpoint>({
      collection: rxDBInstance.collections.tags,
      replicationIdentifier: tagReplicationIdentifier,
      live: true,
      retryTime: retryTimes.tags,
      waitForLeadership: false,
      autoStart: true,
      deletedField: 'deleted',
      pull: {
        batchSize: 10,
        async handler(checkpointOrNull, batchSize){
          const updatedAt = checkpointOrNull ? checkpointOrNull.updatedAt : 0;
          const response = await fetch(`${endpoint}/api/sync/tags?updatedAt=${updatedAt}&limit=${batchSize}`);
          const data = await response.json();
          console.warn('------------- createTagReplication pull --------------', data)
          // 此处返回的 data 将被自动处理，默认是用服务端数据自动覆盖
          return {
            documents: data.documents,
            checkpoint: data.checkpoint
          };
        },
        stream$: tagPullStream$.asObservable()
      },
      push: {
        batchSize: 10,
        async handler(changeRows) {
          /**
           * Push the local documents to a remote REST server.
           */
          if (!user.value?.id) {
            return;
          }

          changeRows = changeRows.filter(row => row.newDocumentState.userId === user.value?.id);
          const rawResponse = await fetch(`${endpoint}/api/sync/tags`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ changeRows })
          });
          /**
           * Contains an array with all conflicts that appeared during this push.
           * If there were no conflicts, return an empty array.
           */
          // 默认的冲突处理程序将始终丢弃 fork 状态并使用 master 状态。这可确保长时间脱机的客户端在再次联机时不会意外覆盖其他人的更改。
          // 您可以通过在调用 addCollection() 时设置属性 conflictHandler 来指定自定义冲突处理程序。
          const conflictsArray = await rawResponse.json();
          console.warn("----------- conflictsArray ----------", conflictsArray)
          return conflictsArray;
        },
      },
    });
  } catch (e) {
    tagReplication = undefined;
    console.error(e);
  }
  return tagReplication;
}

async function createSharedReplication() {
  if (sharedReplication) {
    return sharedReplication;
  }
  console.warn('-------------- createSharedReplication -------------')
  try {
    if (!rxDBInstance?.collections?.shareds) {
      console.log('createSharedReplication: collection not found');
      return;
    }
    sharedReplication = replicateRxCollection<any, RxDBSharedCheckpoint>({
      collection: rxDBInstance.collections.shareds,
      replicationIdentifier: sharedReplicationIdentifier,
      live: true,
      retryTime: retryTimes.shareds,
      waitForLeadership: false,
      autoStart: true,
      deletedField: 'deleted',
      pull: {
        batchSize: 10,
        async handler(checkpointOrNull, batchSize){
          const updatedAt = checkpointOrNull ? checkpointOrNull.updatedAt : 0;
          const response = await fetch(`${endpoint}/api/sync/shareds?updatedAt=${updatedAt}&limit=${batchSize}`);
          const data = await response.json();
          console.warn('------------- createSharedReplication pull --------------', data)
          // 此处返回的 data 将被自动处理，默认是用服务端数据自动覆盖
          return {
            documents: data.documents,
            checkpoint: data.checkpoint
          };
        },
        stream$: sharedPullStream$.asObservable()
      },
      push: {
        batchSize: 10,
        async handler(changeRows) {
          /**
           * Push the local documents to a remote REST server.
           */
          if (!user.value?.id) {
            return;
          }

          changeRows = changeRows.filter(row => row.newDocumentState.userId === user.value?.id);
          const rawResponse = await fetch(`${endpoint}/api/sync/shareds`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ changeRows })
          });
          /**
           * Contains an array with all conflicts that appeared during this push.
           * If there were no conflicts, return an empty array.
           */
          // 默认的冲突处理程序将始终丢弃 fork 状态并使用 master 状态。这可确保长时间脱机的客户端在再次联机时不会意外覆盖其他人的更改。
          // 您可以通过在调用 addCollection() 时设置属性 conflictHandler 来指定自定义冲突处理程序。
          const conflictsArray = await rawResponse.json();
          console.warn("----------- conflictsArray ----------", conflictsArray)
          return conflictsArray;
        },
      },
    });
  } catch (e) {
    sharedReplication = undefined;
    console.error(e);
  }
  return sharedReplication;
}

