// 导出书签，未测试
// function exportBookmarks() {
//   chrome.bookmarks.getTree(function(bookmarkTreeNodes) {
//     const bookmarksData = traverseBookmarkTree(bookmarkTreeNodes);
//     const json = JSON.stringify(bookmarksData, null, 2);
//     const blob = new Blob([json], { type: 'application/json' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = 'bookmarks.json';
//     a.click();
//     URL.revokeObjectURL(url);
//   });
// }

// function traverseBookmarkTree(nodes) {
//   return nodes.map(node => {
//     if (node.children) {
//       return {
//         title: node.title,
//         children: traverseBookmarkTree(node.children)
//       };
//     } else {
//       return {
//         title: node.title,
//         url: node.url
//       };
//     }
//   });
// }

// // 导入书签，未测试
// function importBookmarks(file) {
//   const reader = new FileReader();
//   reader.onload = function(event) {
//     const bookmarksData = JSON.parse(event.target.result);
//     createBookmarks(bookmarksData, 'root');
//   };
//   reader.readAsText(file);
// }

// function createBookmarks(bookmarks, parentId) {
//   bookmarks.forEach(bookmark => {
//     if (bookmark.children) {
//       const folder = chrome.bookmarks.create({
//         title: bookmark.title,
//         parentId: parentId
//       });
//       createBookmarks(bookmark.children, folder.id);
//     } else {
//       chrome.bookmarks.create({
//         title: bookmark.title,
//         url: bookmark.url,
//         parentId: parentId
//       });
//     }
//   });
// }

// 通用函数，用于扁平化 chrome.bookmarks.BookmarkTreeNode 树形结构
export function flattenBookmarks(bookmarks: chrome.bookmarks.BookmarkTreeNode[]) {
  return bookmarks.reduce((acc: chrome.bookmarks.BookmarkTreeNode[], item: chrome.bookmarks.BookmarkTreeNode) => {
    acc.push(item)
    if (item.children) {
      acc.push(...flattenBookmarks(item.children))
    }
    return acc
  }, [])
}

// 新增递归查找父级 ids 的方法
export async function getAllParentIds(folderId: string) {
  const parentIds = [];
  let currentParentId = folderId;
  
  while (currentParentId) {
    const [folder] = await browser.bookmarks.get(currentParentId);
    if (!folder.parentId) break;
    
    parentIds.push(folder.parentId);
    currentParentId = folder.parentId;
  }
  
  return parentIds;
}

// 在已加载到内存中的书签树结构中递归查找父级路径 ids
export function findParentPath(
  nodes: chrome.bookmarks.BookmarkTreeNode[],
  targetId: string,
  path: string[] = []
): string[] {
  for (const node of nodes) {
    if (node.id === targetId) {
      return [...path, node.id];
    }
    if (node.children) {
      const result = findParentPath(node.children, targetId, [...path, node.id]);
      if (result) {
        return result;
      }
    }
  }
  return [];
}

// 递归计算子节点的所有文件夹数和 bookmarks 数
export async function countBookmarksAndFoldersById(currentFolderId: string) {
  const start = performance.now();
  let folderCount = 0;
  let bookmarkCount = 0;

  const bookmarks = await browser.bookmarks.getSubTree(currentFolderId)
  function traverse(node: chrome.bookmarks.BookmarkTreeNode) {
    // 如果当前节点有 url 属性，那么它是一个书签
    if (node.url) {
      bookmarkCount++;
    } else if (node.children && node.children.length > 0) {
      // 如果当前节点有 children 属性且不为空，那么它是一个文件夹
      folderCount++;
      node.children.forEach(traverse); // 递归遍历子节点
    }
  }

  function findAndCount(node: chrome.bookmarks.BookmarkTreeNode) {
    if (node.id === currentFolderId) {
      traverse(node); // 找到目标节点，开始遍历其子节点
    } else if (node.children) {
      // 遍历当前节点的子节点
      node.children.forEach(findAndCount);
    }
  }

  // 从根节点开始查找目标节点及其子树
  bookmarks.forEach(findAndCount);
  const end = performance.now();
  console.log(`folderCount took ${(end - start).toFixed(0)} milliseconds`)
  return { folderCount, bookmarkCount };
}

// 递归获取平铺书签的函数
export async function getFlattenedSubTree(folderId: string) {
  const start = performance.now();
  const tree = await browser.bookmarks.getSubTree(folderId)
  const flatten = (nodes: chrome.bookmarks.BookmarkTreeNode[]): chrome.bookmarks.BookmarkTreeNode[] => {
    return nodes.flatMap(node => {
      const children = node.children ? flatten(node.children) : []
      return [node, ...children]
    })
  }
  const end = performance.now();
  console.log(`getFlattenedSubTree took ${(end - start).toFixed(0)} milliseconds`)
  return flatten(tree)
}

// 递归获取所有书签ID（包含子文件夹）
export async function getAllBookmarkIds(folderId: string): Promise<string[]> {
  const nodes = await browser.bookmarks.getSubTree(folderId);
  console.warn('--------- nodes -----------', nodes)
  const bookmarkIds: string[] = [];

  function traverse(node: chrome.bookmarks.BookmarkTreeNode) {
    if (node.url) { // 书签节点
      bookmarkIds.push(node.id);
    } else if (node.children) { // 文件夹节点
      node.children.forEach(traverse);
    }
  }

  nodes.forEach(traverse);
  return bookmarkIds;
}

// 书签排序函数
export function sortBookmarks(
  items: chrome.bookmarks.BookmarkTreeNode[],
  sortBy: { field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed'; direction: 'asc' | 'desc' }
): chrome.bookmarks.BookmarkTreeNode[] {
  // 仅在非默认排序时进行排序
  if (!sortBy.field) return [...items];

  return [...items].sort((a, b) => {
    let compareResult = 0;
    
    switch (sortBy.field) {
      case 'default':
        compareResult = (a.index || 0) - (b.index || 0);
        break;
      case 'id':
        const numA = parseInt(a.id, 10);
        const numB = parseInt(b.id, 10);
        compareResult = (numA - numB) || a.id.localeCompare(b.id);
        break;
      case 'title':
        compareResult = (a.title || '').localeCompare(b.title || '');
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'dateAdded':
        compareResult = (a.dateAdded || 0) - (b.dateAdded || 0);
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'dateLastUsed':
        compareResult = ((a as any)?.dateLastUsed || 0) - ((b as any)?.dateLastUsed || 0);
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
      case 'url':
        const normalizeUrl = (url?: string) => url?.replace(/^https?:\/\//i, '').toLowerCase() || '';
        compareResult = normalizeUrl(a.url).localeCompare(normalizeUrl(b.url));
        if (compareResult === 0) {
          const numA = parseInt(a.id, 10);
          const numB = parseInt(b.id, 10);
          compareResult = (numA - numB) || a.id.localeCompare(b.id);
        }
        break;
    }
    
    return sortBy.direction === 'asc' ? compareResult : -compareResult;
  });
}

// 打开书签管理器
export function openOrActivateManage(folderId?: string, bookmarkId?: string) {
  const params = new URLSearchParams();

  if (folderId) {
    params.append('id', folderId);
  }

  if (bookmarkId) {
    params.append('bookmarkId', bookmarkId);
  }

  const queryString = params.toString() ? `?${params.toString()}` : '';
  const fullUrl = browser.runtime.getURL(`/dashboard.html#/browser${queryString}`);
  
  // 修改查询逻辑，只匹配基础URL
  browser.tabs.query({}, (tabs) => {
    const bookmarksTab = tabs.find(t => 
      t.url?.startsWith(browser.runtime.getURL('/dashboard.html'))
    );

    if (bookmarksTab?.id) {
      // 激活已存在标签页
      browser.tabs.update(bookmarksTab.id, { 
        url: fullUrl, //如果不带 url 参数，打开的页面不会刷新
        active: true,
      });
    } else {
      browser.tabs.create({ url: fullUrl, active: true });
    }
    // 关闭 sidepanel
    window.close();
  });
}