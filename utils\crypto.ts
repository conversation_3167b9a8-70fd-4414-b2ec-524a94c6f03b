// 现在我要做一个加密书签的功能，大概是像 bitwarden 那样，用户用一个简单密码，解密真正的密钥，再用真正的密钥去解密所有书签，即便用户修改了密码，也无需修改书签的加密字符串，那么，请问整个技术栈应该是怎么样的？
// 要实现类似Bitwarden的加密书签功能，这里推荐一个完整的技术栈方案：
// ### **带中间密钥的最简高性能方案**  
// （平衡安全性与性能，支持密码修改无需重加密书签）
// ---
// ### ** 完整加密书签方案（支持多设备同步，支持密码修改无需重加密书签）**
// #### **核心设计**
// 1. **二层密钥体系**  
//    - 主密码 → PBKDF2派生 **主密钥**（加密`dataKey`用）  
//    - 随机生成 **dataKey**（实际加密书签）  
//    - 每条书签使用 **独立随机IV**  
// 2. **多设备同步友好**  
//    - 所有加密元数据（IV、盐值、加密后的dataKey）均存数据库  
//    - 修改密码无需重新加密书签  

// 1. 核心概念澄清
// 您描述的是典型的两层密钥结构，但需要明确以下术语：
// 主密码 (Master Password)：用户记忆的密码（如 "myStrongPassword123"）
// 主密钥 (Master Key)：通过 PBKDF2/Argon2 从主密码派生的密钥（用于加密 dataKey）
// 数据密钥 (Data Key)：随机生成的 AES 密钥（实际加密书签数据）
// 加密后的数据密钥 (encryptedDataKey)：用主密钥加密 dataKey 的结果

// A[主密码] -->|PBKDF2| B[主密钥]
// C[随机生成] --> D[dataKey]
// B -->|AES加密| E[encryptedDataKey]
// D -->|AES加密| F[书签数据]

// ---

// ### **1. 数据库结构设计**
// #### **用户表（users）**
// ```language=javascript
// {
//   userId: "唯一用户ID（如UUID）",
//   encryptSalt: "PBKDF2盐值（每个用户独立）", // 16+字节随机值
//   encryptKey: "用主密钥加密后的dataKey", // ArrayBuffer或Base64
//   encryptIv: "加密dataKey时使用的IV",        // 16字节
//   encryptAlgo: "AES-GCM-128",         // 算法标识（未来兼容）
// }

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  return btoa(String.fromCharCode(...new Uint8Array(buffer)));
}

export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

export const generateIv = () => {
  return crypto.getRandomValues(new Uint8Array(12));
};

// GCM加密（自动包含认证标签）
export const encryptGCM = async (data: string, iv: string, key: CryptoKey) => {
  const encoded = new TextEncoder().encode(data);
  const ciphertext = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv: base64ToArrayBuffer(iv) },
    key,
    encoded
  );
  return {
    ciphertext: arrayBufferToBase64(ciphertext),
    iv
  };
};

// GCM解密（自动验证完整性）
export const decryptGCM = async (ciphertext: string, iv: string, key: CryptoKey) => {
  const result = await crypto.subtle.decrypt(
    { name: 'AES-GCM', iv: base64ToArrayBuffer(iv) },
    key,
    base64ToArrayBuffer(ciphertext)
  );
  return new TextDecoder().decode(result);
};

// 派生主密钥
export async function deriveMasterKey(masterPassword: string, salt: Uint8Array): Promise<CryptoKey> {
  return await crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt,
      iterations: 50000,
      hash: "SHA-256"
    },
    await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(masterPassword),
      { name: "PBKDF2" },
      false,
      ["deriveKey"]
    ),
    { name: "AES-GCM", length: 128 },
    false,
    ["encrypt", "decrypt"]
  );
}

// 设置主密码，生成随机加密密钥
export async function generateEncryptKey(masterPassword: string) {
  // 1. 生成随机 dataKey（实际加密书签）
  const dataKey = await crypto.subtle.generateKey(
    { name: "AES-GCM", length: 128 },
    true,
    ["encrypt", "decrypt"]
  );

  // 2. 派生主密钥
  const salt = crypto.getRandomValues(new Uint8Array(12));
  const masterKey = await deriveMasterKey(masterPassword, salt);

  // 3. 使用 encryptGCM 加密存储 dataKey
  const iv = generateIv();
  const { ciphertext: encryptedDataKey, iv: encryptedDataKeyIV } = await encryptGCM(
    arrayBufferToBase64(await crypto.subtle.exportKey("raw", dataKey)),
    arrayBufferToBase64(iv.buffer),
    masterKey
  );

  return {
    dataKey,
    encryptSalt: arrayBufferToBase64(salt.buffer),
    encryptKey: encryptedDataKey,
    encryptIv: encryptedDataKeyIV,
    encryptAlgo: "AES-GCM-128",
  };
}

/**
 * 修改主密码，加密密钥不变（无需重新加密数据）
 * @param newPassword 新密码
 * @param oldDataKey 原实际加密密钥
 * @param oldEncryptSalt 旧盐值
 * @returns 更新后的加密元数据 { encryptKey, encryptIv }
 */
export async function resetEncryptKey(
  newPassword: string,
  oldDataKey: CryptoKey,
  oldEncryptSalt: string,
): Promise<{ encryptKey: string; encryptIv: string }> {
  try {
    // 使用旧的 oldDataKey 的 string 值
    const dataKey = await crypto.subtle.exportKey('raw', oldDataKey);
    const dataKeyString = arrayBufferToBase64(dataKey);

    // 1. 用新密码和原盐值派生新主密钥
    const newMasterKey = await deriveMasterKey(newPassword, new Uint8Array(base64ToArrayBuffer(oldEncryptSalt)));

    // 2. 重新加密 dataKey
    const iv = generateIv();
    const { ciphertext: newEncryptKey, iv: newEncryptIv } = await encryptGCM(
      dataKeyString,
      arrayBufferToBase64(iv.buffer),
      newMasterKey
    );

    return { encryptKey: newEncryptKey, encryptIv: newEncryptIv };
  } catch (error) {
    throw new Error("Failed to change password: " + error);
  }
}

/**
 * 客户端获取/恢复实际数据加密密钥
 * @param masterPassword 主密码
 * @param encryptSalt 盐值
 * @param encryptKey 加密密钥
 * @param encryptIv 加密 IV
 * @returns 真正的数据加密密钥
 */
export async function restoreDataKey(
  masterPassword: string,
  encryptSalt: string,
  encryptKey: string,
  encryptIv: string
): Promise<CryptoKey> {
  try {
    // 1. 派生主密钥
    const masterKey = await deriveMasterKey(masterPassword, new Uint8Array(base64ToArrayBuffer(encryptSalt)));

    // 2. 解密 encryptKey
    const dataKeyStr = await decryptGCM(encryptKey, encryptIv, masterKey);

    // 3. 导入 dataKey
    const dataKeyBuffer = base64ToArrayBuffer(dataKeyStr);
    return await crypto.subtle.importKey(
      "raw",
      dataKeyBuffer,
      { name: "AES-GCM", length: 128 },
      true,
      ["encrypt", "decrypt"]
    );
  } catch (error) {
    throw new Error("Failed to decrypt dataKey: " + error);
  }
}
