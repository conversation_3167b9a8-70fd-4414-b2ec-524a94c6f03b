const endpoint = import.meta.env.VITE_ENDPOINT;
let eventSource: EventSource | null = null;
import { extraPullStream$, tagPullStream$, sharedPullStream$ } from '../rxdb/sync';

export async function openSSE() {
  if (eventSource) {
    return;
  }
  
  const connect = () => {
    eventSource = new EventSource(`${endpoint}/api/sse`, { withCredentials: true });
    console.warn('------- openSSE connect ---------');
    
    // sse 第一次接收到下发信息后才触发连接建立事件
    eventSource.onopen = (event) => {
      console.warn('------- sse 连接成功 ---------', event);
    };
    
    eventSource.onerror = (e) => {
      console.error('SSE连接错误，尝试重连...', e);
      if (eventSource) {
        eventSource.close();
        eventSource = null;
        // 使用指数退避策略重连，最多等待30秒
        setTimeout(connect, Math.min(retryDelay * 2, 300000));
      }
    };

    eventSource.onmessage = (event) => {
      console.warn('------- 收到 sse 消息 ---------', event.data);
      if (event.data === 'RESYNC') {
        extraPullStream$.next('RESYNC');
        tagPullStream$.next('RESYNC');
        sharedPullStream$.next('RESYNC');
      }
      // 重置重连延迟时间
      retryDelay = 1000;
    };
  };

  // 初始重连延迟时间
  let retryDelay = 1000;
  connect();
}

export async function closeSSE() {
  if (eventSource) {
    eventSource.close();
    eventSource = null;
  }
}