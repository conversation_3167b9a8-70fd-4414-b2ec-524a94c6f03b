import type { TreeItem } from './dnd'

// 这个 tree 对象是一个工具集合，用于操作树形结构数据，并没有用上
export const tree = {
  remove(data: TreeItem[], id: string): TreeItem[] {
    return data
      .filter(item => item.title !== id)
      .map((item) => {
        if (tree.hasChildren(item)) {
          return {
            ...item,
            children: tree.remove(item.children ?? [], id),
          }
        }
        return item
      })
  },
  insertBefore(data: TreeItem[], targetId: string, newItem: TreeItem): TreeItem[] {
    return data.flatMap((item) => {
      if (item.title === targetId)
        return [newItem, item]

      if (tree.hasChildren(item)) {
        return {
          ...item,
          children: tree.insertBefore(item.children ?? [], targetId, newItem),
        }
      }
      return item
    })
  },
  insertAfter(data: TreeItem[], targetId: string, newItem: TreeItem): TreeItem[] {
    return data.flatMap((item) => {
      if (item.title === targetId)
        return [item, newItem]

      if (tree.hasChildren(item)) {
        return {
          ...item,
          children: tree.insertAfter(item.children ?? [], targetId, newItem),
        }
      }

      return item
    })
  },
  insertChild(data: TreeItem[], targetId: string, newItem: TreeItem): TreeItem[] {
    return data.flatMap((item) => {
      if (item.title === targetId) {
        // already a parent: add as first child
        return {
          ...item,
          // opening item so you can see where item landed
          isOpen: true,
          children: [newItem, ...item.children ?? []],
        }
      }

      if (!tree.hasChildren(item))
        return item

      return {
        ...item,
        children: tree.insertChild(item.children ?? [], targetId, newItem),
      }
    })
  },
  find(data: TreeItem[], itemId: string): TreeItem | undefined {
    for (const item of data) {
      if (item.title === itemId)
        return item

      if (tree.hasChildren(item)) {
        const result = tree.find(item.children ?? [], itemId)
        if (result)
          return result
      }
    }
  },
  getPathToItem({
    current,
    targetId,
    parentIds = [],
  }: {
    current: TreeItem[]
    targetId: string
    parentIds?: string[]
  }): string[] | undefined {
    for (const item of current) {
      if (item.title === targetId)
        return parentIds

      const nested = tree.getPathToItem({
        current: (item.children ?? []),
        targetId,
        parentIds: [...parentIds, item.title],
      })
      if (nested)
        return nested
    }
  },
  hasChildren(item: TreeItem): boolean {
    return (item.children ?? []).length > 0
  },
}